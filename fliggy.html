<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OTA Order Converter - Fliggy订单处理</title>
    <!-- API配置文件 - 统一管理API密钥 -->
    <script src="api-config.js"></script>
    <style>
        /* 基础样式 */
        :root {
            --primary-color: #3b82f6;
            --primary-hover: #2563eb;
            --secondary-color: #6b7280;
            --secondary-hover: #4b5563;
            --success-color: #10b981;
            --success-hover: #059669;
            --danger-color: #ef4444;
            --danger-hover: #dc2626;
            --warning-color: #f59e0b;
            --warning-hover: #d97706;
            --border-color: #e5e7eb;
            --bg-color: #f9fafb;
            --text-color: #111827;
            --text-secondary: #6b7280;
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-md: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --radius-sm: 0.25rem;
            --radius: 0.375rem;
            --radius-md: 0.5rem;
            --radius-lg: 0.75rem;
            --transition: all 0.2s ease-in-out;
        }
        
        * {
            box-sizing: border-box;
            -webkit-tap-highlight-color: transparent; /* 移除移动端点击高亮 */
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            background-color: var(--bg-color);
            color: var(--text-color);
            margin: 0;
            padding: 0;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            line-height: 1.5;
            font-size: 16px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 1rem;
            width: 100%;
            box-sizing: border-box;
        }
        
        header {
            background-color: white;
            border-bottom: 1px solid var(--border-color);
            padding: 1rem 0;
            box-shadow: var(--shadow-sm);
            position: -webkit-sticky;
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo-container {
            display: flex;
            align-items: center;
        }
        
        .logo-container img {
            height: 2rem;
            margin-right: 0.75rem;
            transition: var(--transition);
        }
        
        h1 {
            font-size: 1.25rem;
            font-weight: 600;
            margin: 0;
            color: var(--primary-color);
            transition: var(--transition);
        }
        
        main {
            flex: 1;
            padding: 1.5rem 0;
        }
        
        .card {
            background-color: white;
            border-radius: var(--radius-md);
            border: 1px solid var(--border-color);
            overflow: hidden;
            margin-bottom: 1.5rem;
            box-shadow: var(--shadow-sm);
            transition: var(--transition);
        }
        
        .card:hover {
            box-shadow: var(--shadow);
        }
        
        .card-header {
            padding: 1.25rem;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: rgba(59, 130, 246, 0.03);
        }
        
        .card-title {
            font-size: 1.125rem;
            font-weight: 600;
            margin: 0;
            color: var(--primary-color);
        }
        
        .card-body {
            padding: 1.25rem;
        }
        
        .form-group {
            margin-bottom: 1rem;
            position: relative;
        }
        
        .form-label {
            display: block;
            font-size: 0.875rem;
            font-weight: 500;
            color: var(--text-secondary);
            margin-bottom: 0.25rem;
            transition: var(--transition);
        }
        
        .form-input, textarea, select {
            width: 100%;
            padding: 0.625rem 0.75rem;
            border: 1px solid var(--border-color);
            border-radius: var(--radius);
            background-color: #fff;
            color: var(--text-color);
            font-size: 1rem;
            line-height: 1.5;
            transition: var(--transition);
            box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.05);
        }
        
        .form-input:hover, textarea:hover, select:hover {
            border-color: var(--primary-color);
        }
        
        .form-input:focus, textarea:focus, select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.25);
        }
        
        .form-input[readonly], .form-input:disabled, textarea[readonly], select:disabled {
            background-color: var(--bg-color);
            cursor: not-allowed;
            opacity: 0.75;
            box-shadow: none;
        }
        
        textarea {
            min-height: 150px;
            resize: vertical;
            line-height: 1.6;
        }
        
        /* 移动端文本区域样式 */
        @media (max-width: 767px) {
            textarea {
                min-height: 120px;
            }
        }
        
        .btn {
            display: inline-flex;
            font-weight: 500;
            justify-content: center;
            align-items: center;
            white-space: nowrap;
            vertical-align: middle;
            -webkit-user-select: none;
            user-select: none;
            border: 1px solid transparent;
            padding: 0.625rem 1.25rem;
            font-size: 1rem;
            line-height: 1.5;
            border-radius: var(--radius);
            transition: var(--transition);
            cursor: pointer;
            position: relative;
            overflow: hidden;
            text-decoration: none;
            box-shadow: var(--shadow-sm);
        }
        
        .btn::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 5px;
            height: 5px;
            background: rgba(255, 255, 255, 0.5);
            opacity: 0;
            border-radius: 100%;
            transform: scale(1, 1) translate(-50%);
            transform-origin: 50% 50%;
        }
        
        .btn:focus:not(:active)::after {
            animation: ripple 1s ease-out;
        }
        
        @keyframes ripple {
            0% {
                transform: scale(0, 0);
                opacity: 0.5;
            }
            20% {
                transform: scale(25, 25);
                opacity: 0.3;
            }
            100% {
                opacity: 0;
                transform: scale(40, 40);
            }
        }
        
        .btn-primary {
            color: #fff;
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }
        
        .btn-primary:hover {
            background-color: var(--primary-hover);
            border-color: var(--primary-hover);
            box-shadow: var(--shadow);
            transform: translateY(-1px);
        }
        
        .btn-primary:active {
            transform: translateY(0);
            box-shadow: var(--shadow-sm);
        }
        
        .btn-gray {
            color: #fff;
            background-color: var(--secondary-color);
            border-color: var(--secondary-color);
        }
        
        .btn-gray:hover {
            background-color: var(--secondary-hover);
            border-color: var(--secondary-hover);
            box-shadow: var(--shadow);
            transform: translateY(-1px);
        }
        
        .btn-gray:active {
            transform: translateY(0);
            box-shadow: var(--shadow-sm);
        }
        
        .btn-green {
            color: #fff;
            background-color: var(--success-color);
            border-color: var(--success-color);
        }
        
        .btn-green:hover {
            background-color: var(--success-hover);
            border-color: var(--success-hover);
            box-shadow: var(--shadow);
            transform: translateY(-1px);
        }
        
        .btn-green:active {
            transform: translateY(0);
            box-shadow: var(--shadow-sm);
        }
        
        .button-group {
            display: flex;
            gap: 0.5rem;
            margin-top: 1rem;
        }
        
        /* 移动端按钮组样式 */
        @media (max-width: 767px) {
            .button-group {
                flex-direction: column;
            }
            
            .button-group .btn {
                width: 100%;
                margin-bottom: 0.5rem;
                min-height: 44px; /* 确保触摸目标足够大 */
            }
        }
        
        .flex-1 {
            flex: 1;
        }
        
        .hidden {
            display: none;
        }
        
        .grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 1.5rem;
        }
        
        @media (min-width: 1024px) {
            .grid {
                grid-template-columns: 1fr 1fr;
            }
        }
        
        .form-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 1rem;
        }
        
        @media (min-width: 768px) {
            .form-grid {
                grid-template-columns: 1fr 1fr;
            }
        }
        
        /* 移动端适配 */
        @media (max-width: 767px) {
            .container {
                padding: 0 0.5rem;
            }
            
            .card-body {
                padding: 1rem;
            }
            
            .header-content {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .header-content a {
                margin-top: 0.5rem;
            }
            
            h1 {
                font-size: 1rem;
                margin-top: 0.5rem;
            }
            
            .logo-container img {
                height: 1.5rem;
            }
        }
        
        .col-span-2 {
            grid-column: span 1;
        }
        
        @media (min-width: 768px) {
            .col-span-2 {
                grid-column: span 2;
            }
        }
        
        footer {
            margin-top: auto;
            padding: 1rem 0;
            background-color: white;
            border-top: 1px solid #e5e7eb;
            /* 兼容Edge浏览器 */
            font-size: 0.875rem;
            color: #6b7280;
        }
        
        footer .container {
            /* 兼容旧版Edge浏览器，使用替代方案实现文本居中 */
            display: flex;
            justify-content: center;
        }
        
        .notification {
            position: fixed;
            bottom: 1.5rem;
            right: 1.5rem;
            background-color: var(--success-color);
            color: white;
            padding: 0.75rem 1.25rem;
            border-radius: var(--radius);
            box-shadow: var(--shadow-md);
            opacity: 0;
            transform: translateY(20px);
            transition: opacity 0.3s ease-in-out, transform 0.3s ease-in-out;
            z-index: 50;
            max-width: 350px;
            font-weight: 500;
            display: flex;
            align-items: center;
        }
        
        .notification::before {
            content: '✔';
            margin-right: 0.5rem;
            font-weight: bold;
        }
        
        /* 移动端通知样式 */
        @media (max-width: 767px) {
            .notification {
                bottom: 1rem;
                right: 1rem;
                left: 1rem;
                font-size: 0.875rem;
                padding: 0.75rem 1rem;
                max-width: none;
                border-radius: var(--radius-sm);
            }
        }
        
        .notification.show {
            opacity: 1;
            transform: translateY(0);
        }
        
        .order-item {
            border: 1px solid var(--border-color);
            border-radius: var(--radius-md);
            padding: 1.25rem;
            transition: var(--transition);
            background-color: white;
            box-shadow: var(--shadow-sm);
            position: relative;
            overflow: hidden;
        }
        
        .order-item::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background-color: var(--primary-color);
            opacity: 0;
            transition: var(--transition);
        }
        
        .order-item:hover {
            transform: translateY(-3px);
            box-shadow: var(--shadow-md);
        }
        
        .order-item:hover::after {
            opacity: 1;
        }
        
        /* 多订单样式 */
        .order-card {
            background: white;
            border-radius: var(--radius-md);
            box-shadow: var(--shadow-sm);
            padding: 1.25rem;
            transition: var(--transition);
            border: 1px solid var(--border-color);
            position: relative;
            overflow: hidden;
        }
        
        .order-card::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background-color: var(--primary-color);
            opacity: 0;
            transition: var(--transition);
        }
        
        .order-card:hover {
            transform: translateY(-3px);
            box-shadow: var(--shadow-md);
        }
        
        .order-card:hover::after {
            opacity: 1;
        }
        
        .order-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.5rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid #eee;
        }
        
        .order-content p {
            margin: 0.3rem 0;
            font-size: 0.9rem;
        }
        
        /* 移动端多订单样式 */
        @media (max-width: 767px) {
            #orders-list {
                grid-template-columns: 1fr !important;
                gap: 0.75rem !important;
                padding: 0.75rem !important;
            }
            
            .order-item {
                padding: 0.75rem;
            }
            
            .order-item-mobile .order-header {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .order-item-mobile .order-header button {
                margin-top: 0.5rem;
                width: 100%;
            }
            
            .order-item-mobile .order-content {
                display: flex;
                flex-direction: column;
            }
            
            /* 表单元素在移动端的样式 */
            .form-input, textarea, select {
                font-size: 16px; /* 防止iOS自动缩放 */
                padding: 0.625rem;
            }
            
            /* 移动端卡片样式优化 */
            .card {
                border-radius: 0.375rem;
                margin-bottom: 1rem;
            }
            
            .card-header {
                padding: 0.875rem 1rem;
            }
            
            .card-title {
                font-size: 1rem;
            }
            
            /* 移动端按钮优化 */
            .btn {
                min-height: 44px; /* 确保触摸目标足够大 */
                display: flex;
                align-items: center;
                justify-content: center;
            }
        }
        
        /* 等待动画样式 */
        #api-loader {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(255, 255, 255, 0.85);
            -webkit-backdrop-filter: blur(3px);
            backdrop-filter: blur(3px);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
            display: none;
            opacity: 0;
            transition: opacity 0.3s ease-in-out;
        }
        
        #api-loader.show {
            opacity: 1;
        }
        
        #api-loader .spinner-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            background-color: white;
            padding: 1.5rem;
            border-radius: var(--radius-md);
            box-shadow: var(--shadow-md);
        }
        
        #api-loader .spinner {
            border: 3px solid rgba(59, 130, 246, 0.2);
            border-top: 3px solid var(--primary-color);
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin-bottom: 1rem;
        }
        
        #api-loader .spinner-text {
            font-size: 0.875rem;
            color: var(--text-secondary);
            font-weight: 500;
        }
        
        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }
            100% {
                transform: rotate(360deg);
            }
        }
        
        /* 添加深色模式支持 */
        @media (prefers-color-scheme: dark) {
            :root {
                --bg-color: #111827;
                --text-color: #f9fafb;
                --text-secondary: #9ca3af;
                --border-color: #374151;
            }
            
            body {
                background-color: var(--bg-color);
                color: var(--text-color);
            }
            
            header, footer, .card, .order-item, .order-card {
                background-color: #1f2937;
                border-color: var(--border-color);
            }
            
            .form-input, textarea, select {
                background-color: #111827;
                color: var(--text-color);
                border-color: var(--border-color);
            }
            
            .form-input[readonly], .form-input:disabled, textarea[readonly], select:disabled {
                background-color: rgba(17, 24, 39, 0.7);
            }
            
            #api-loader {
                background-color: rgba(17, 24, 39, 0.85);
            }
            
            #api-loader .spinner-container {
                background-color: #1f2937;
            }
            
            /* 深色模式下的特殊优化 */
            .btn {
                box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
            }
            
            .notification {
                background-color: #065f46; /* 深色模式下的成功颜色 */
            }
            
            .order-item::after, .order-card::after {
                background-color: #3b82f6; /* 保持主色调可见性 */
                opacity: 0.2;
            }
            
            .order-item:hover::after, .order-card:hover::after {
                opacity: 1;
            }
        }
        
        /* 减少动画，提高性能模式 */
        @media (prefers-reduced-motion: reduce) {
            * {
                transition-duration: 0.01ms !important;
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
            }
            
            .btn:hover, .order-item:hover, .order-card:hover {
                transform: none !important;
            }
        }
    </style>
    <style>
        /* 设备特定样式 */
        /* 移动设备优化 */
        html.mobile .btn, html.tablet .btn {
            min-height: 44px; /* 确保触摸目标足够大 */
            padding: 0.625rem 1rem;
        }
        
        html.mobile .form-input, html.mobile textarea, html.mobile select,
        html.tablet .form-input, html.tablet textarea, html.tablet select {
            font-size: 16px; /* 防止iOS自动缩放 */
            padding: 0.625rem;
        }
        
        html.mobile #orders-list, html.tablet #orders-list {
            grid-template-columns: 1fr;
            gap: 0.75rem;
            padding: 0.75rem;
        }
        
        html.mobile .order-item, html.tablet .order-item {
            padding: 0.75rem;
        }
        
        html.mobile .notification, html.tablet .notification {
            left: 1rem;
            right: 1rem;
            bottom: 1rem;
            max-width: none;
        }
        
        /* Edge浏览器特定优化 */
        html.edge .btn {
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        html.edge body {
            text-rendering: optimizeLegibility;
        }
        
        /* 减少动画，提高性能模式 */
        @media (prefers-reduced-motion: reduce) {
            * {
                transition-duration: 0.01ms !important;
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
            }
            
            .btn:hover, .order-item:hover, .order-card:hover {
                transform: none !important;
            }
        }
    </style>
</head>
<body>
    <div class="notification" id="notification"></div>
    
    <header>
        <div class="container header-content">
            <div class="logo-container">
                <img src="images/logo.svg" alt="Logo">
                <h1>OTA Order Converter - Fliggy订单处理</h1>
            </div>
            <a href="index.html" class="btn btn-gray" data-i18n="back-to-home">返回首页</a>
        </div>
    </header>

    <main class="container">
        <div class="grid">
            <!-- 输入部分 -->
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title" data-i18n="fliggy-original-order">Fliggy原始订单</h2>
                    <div class="lang-selector">
                        <select id="language-selector" class="form-input" style="width: auto; min-width: 120px;" aria-label="选择语言 Select language">
                            <option value="zh">中文</option>
                            <option value="en">English</option>
                            <option value="jp">日本語</option>
                            <option value="ko">한국어</option>
                        </select>
                    </div>
                </div>
                <div class="card-body">
                    <div class="form-group">
                        <label for="raw-order" class="form-label" data-i18n="original-order-data">原始订单数据</label>
                        <textarea id="raw-order" data-i18n-placeholder="paste-fliggy-order-data" placeholder="粘贴Fliggy原始订单数据..."></textarea>
                    </div>
                    <div class="button-group">
                        <button id="convert-btn" class="btn btn-primary flex-1" data-i18n="process-order">处理订单</button>
                        <button id="reset-btn" class="btn btn-gray" data-i18n="reset">重置</button>
                    </div>
                </div>
            </div>

            <!-- 输出部分 -->
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title" data-i18n="standardized-order">标准化订单</h2>
                    <button id="copy-output" class="btn btn-gray" data-i18n="copy-output">复制</button>
                </div>
                <div class="card-body">
                    <form id="order-form">
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="ota" class="form-label">OTA平台</label>
                                <input type="text" id="ota" class="form-input" readonly>
                            </div>
                            <div class="form-group">
                                <label for="ota-reference" class="form-label">OTA订单号</label>
                                <input type="text" id="ota-reference" class="form-input" readonly>
                            </div>
                            <div class="form-group">
                                <label for="price" class="form-label">价格</label>
                                <input type="text" id="price" class="form-input" readonly>
                            </div>
                            <div class="form-group">
                                <label for="name" class="form-label">乘客姓名</label>
                                <input type="text" id="name" class="form-input" readonly>
                            </div>
                            <div class="form-group">
                                <label for="phone" class="form-label">电话</label>
                                <input type="text" id="phone" class="form-input" readonly>
                            </div>
                            <div class="form-group">
                                <label for="email" class="form-label">邮箱</label>
                                <input type="email" id="email" class="form-input" readonly>
                            </div>
                            <div class="form-group">
                                <label for="flight-number" class="form-label">航班号</label>
                                <input type="text" id="flight-number" class="form-input" readonly>
                            </div>
                            <div class="form-group">
                                <label for="pickup-datetime" class="form-label">接机时间</label>
                                <input type="text" id="pickup-datetime" class="form-input" readonly>
                            </div>
                            <div class="form-group">
                                <label for="pickup-address" class="form-label">接机地址</label>
                                <input type="text" id="pickup-address" class="form-input" readonly>
                            </div>
                            <div class="form-group">
                                <label for="dropoff-address" class="form-label">送机地址</label>
                                <input type="text" id="dropoff-address" class="form-input" readonly>
                            </div>
                            <div class="form-group">
                                <label for="car-type" class="form-label">车型</label>
                                <input type="text" id="car-type" class="form-input" readonly>
                            </div>
                            <div class="form-group">
                                <label for="passenger-number" class="form-label">乘客人数</label>
                                <input type="number" id="passenger-number" class="form-input" readonly>
                            </div>
                            <div class="form-group">
                                <label for="luggage-number" class="form-label">行李数量</label>
                                <input type="number" id="luggage-number" class="form-input" readonly>
                            </div>
                            <div class="form-group">
                                <label for="language" class="form-label">语言</label>
                                <input type="text" id="language" class="form-input" readonly>
                            </div>
                            <div class="form-group">
                                <label for="category" class="form-label">类别</label>
                                <select id="category" class="form-input" disabled>
                                    <option value="airport">机场接送</option>
                                    <option value="charter">包车</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="subcategory" class="form-label">子类别</label>
                                <select id="subcategory" class="form-input" disabled>
                                    <option value="pickup">接机</option>
                                    <option value="dropoff">送机</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="driving-region" class="form-label">驾驶区域</label>
                                <select id="driving-region" class="form-input" disabled>
                                    <option value="kl">吉隆坡</option>
                                    <option value="penang">槟城</option>
                                    <option value="sg">新加坡</option>
                                    <option value="jb">新山</option>
                                    <option value="sabah">沙巴</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="driver" class="form-label">司机数量</label>
                                <input type="number" id="driver" class="form-input" value="1" readonly>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="remark" class="form-label">备注</label>
                            <textarea id="remark" rows="2" class="form-input" readonly></textarea>
                        </div>
                        <div class="button-group">
                            <button type="button" id="edit-btn" class="btn btn-gray flex-1">编辑</button>
                            <button type="button" id="save-btn" class="btn btn-green flex-1 hidden">保存</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- 多订单结果容器 -->
        <div id="multi-orders-container" class="card hidden">
            <div class="card-header">
                <h2 class="card-title" data-i18n="multiple-orders-detected">已识别到的多个订单</h2>
            </div>
            <div id="orders-list" class="card-body" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(280px, 1fr)); gap: 1rem; padding: 1rem;">
                <!-- 订单条目将在这里动态添加 -->
            </div>
        </div>
    </main>

    <footer>
        <div class="container">
            Built with <a href="https://flowith.net" target="_blank" rel="noopener" style="color: #3b82f6; text-decoration: none;">Flowith Oracle</a>.
        </div>
    </footer>

    <script>
        // 多语言支持
        const i18n = {
            zh: {
                // 页面标题
                "fliggy-original-order": "Fliggy原始订单",
                "standardized-order": "标准化订单",
                "multiple-orders-detected": "已识别到的多个订单",
                // 表单标签和占位符
                "original-order-data": "原始订单数据",
                "paste-fliggy-order-data": "粘贴Fliggy原始订单数据...",
                "ota-platform": "OTA平台",
                "order-number": "订单编号",
                "price": "价格",
                "passenger-name": "乘客姓名",
                "phone": "电话",
                "email": "邮箱",
                "flight-number": "航班号",
                "pickup-time": "接机时间",
                "pickup-address": "接机地址",
                "dropoff-address": "送达地址",
                "car-type": "车型",
                "luggage-count": "行李数量",
                "passenger-count": "乘客人数",
                "language": "语言",
                "order-type": "订单类型",
                "driving-region": "驾驶区域",
                "driver-count": "司机数量",
                "remarks": "备注",
                // 按钮
                "process-order": "处理订单",
                "reset": "重置",
                "enable-edit": "启用编辑",
                "save-changes": "保存更改",
                "copy-output": "复制输出",
                "view-details": "查看详细",
                "copy": "复制",
                "back-to-home": "返回首页",
                // 通知
                "please-enter-data": "请输入原始订单数据",
                "data-copied": "数据已复制到剪贴板",
                "edit-enabled": "已启用编辑模式",
                "changes-saved": "更改已保存",
                "multiple-orders-found": "检测到多个订单，正在处理...",
            },
            en: {
                // Page titles
                "fliggy-original-order": "Fliggy Original Order",
                "standardized-order": "Standardized Order",
                "multiple-orders-detected": "Multiple Orders Detected",
                // Form labels and placeholders
                "original-order-data": "Original Order Data",
                "paste-fliggy-order-data": "Paste Fliggy original order data...",
                "ota-platform": "OTA Platform",
                "order-number": "Order Number",
                "price": "Price",
                "passenger-name": "Passenger Name",
                "phone": "Phone",
                "email": "Email",
                "flight-number": "Flight Number",
                "pickup-time": "Pickup Time",
                "pickup-address": "Pickup Address",
                "dropoff-address": "Dropoff Address",
                "car-type": "Car Type",
                "luggage-count": "Luggage Count",
                "passenger-count": "Passenger Count",
                "language": "Language",
                "order-type": "Order Type",
                "driving-region": "Driving Region",
                "driver-count": "Driver Count",
                "remarks": "Remarks",
                // Buttons
                "process-order": "Process Order",
                "reset": "Reset",
                "enable-edit": "Enable Edit",
                "save-changes": "Save Changes",
                "copy-output": "Copy Output",
                "view-details": "View Details",
                "copy": "Copy",
                "back-to-home": "Back to Home",
                // Notifications
                "please-enter-data": "Please enter original order data",
                "data-copied": "Data copied to clipboard",
                "edit-enabled": "Edit mode enabled",
                "changes-saved": "Changes saved",
                "multiple-orders-found": "Multiple orders detected, processing...",
                // Field values
                "airport-pickup": "Airport Pickup",
                "airport-dropoff": "Airport Dropoff",
                "charter": "Charter Service"
            },
            jp: {
                // ページタイトル
                "fliggy-original-order": "Fliggy元注文",
                "standardized-order": "標準化注文",
                "multiple-orders-detected": "検出された複数の注文",
                // フォームラベルとプレースホルダー
                "original-order-data": "元注文データ",
                "paste-fliggy-order-data": "Fliggyの元注文データを貼り付け...",
                "ota-platform": "OTAプラットフォーム",
                "order-number": "注文番号",
                "price": "価格",
                "passenger-name": "乗客名",
                "phone": "電話番号",
                "email": "メール",
                "flight-number": "フライト番号",
                "pickup-time": "送迎時間",
                "pickup-address": "ピックアップ住所",
                "dropoff-address": "目的地住所",
                "car-type": "車種",
                "luggage-count": "荷物数",
                "passenger-count": "乗客数",
                "language": "言語",
                "order-type": "注文タイプ",
                "driving-region": "運転地域",
                "driver-count": "ドライバー数",
                "remarks": "備考",
                // ボタン
                "process-order": "注文処理",
                "reset": "リセット",
                "enable-edit": "編集を有効化",
                "save-changes": "変更を保存",
                "copy-output": "出力をコピー",
                "view-details": "詳細を表示",
                "copy": "コピー",
                "back-to-home": "ホームに戻る",
                // 通知
                "please-enter-data": "元注文データを入力してください",
                "data-copied": "データがクリップボードにコピーされました",
                "edit-enabled": "編集モードが有効になりました",
                "changes-saved": "変更が保存されました",
                "multiple-orders-found": "複数の注文が検出されました、処理中...",
                // フィールド値
                "airport-pickup": "空港送迎",
                "airport-dropoff": "空港へ送り",
                "charter": "チャーターサービス"
            },
            ko: {
                // 페이지 제목
                "fliggy-original-order": "Fliggy 원본 주문",
                "standardized-order": "표준화된 주문",
                "multiple-orders-detected": "감지된 다중 주문",
                // 폼 라벨 및 플레이스홀더
                "original-order-data": "원본 주문 데이터",
                "paste-fliggy-order-data": "Fliggy 원본 주문 데이터 붙여넣기...",
                "ota-platform": "OTA 플랫폼",
                "order-number": "주문 번호",
                "price": "가격",
                "passenger-name": "승객 이름",
                "phone": "전화번호",
                "email": "이메일",
                "flight-number": "항공편 번호",
                "pickup-time": "픽업 시간",
                "pickup-address": "픽업 주소",
                "dropoff-address": "하차 주소",
                "car-type": "차량 유형",
                "luggage-count": "수하물 개수",
                "passenger-count": "승객 수",
                "language": "언어",
                "order-type": "주문 유형",
                "driving-region": "운전 지역",
                "driver-count": "운전자 수",
                "remarks": "비고",
                // 버튼
                "process-order": "주문 처리",
                "reset": "재설정",
                "enable-edit": "편집 활성화",
                "save-changes": "변경사항 저장",
                "copy-output": "출력 복사",
                "view-details": "상세 보기",
                "copy": "복사",
                "back-to-home": "홈으로 돌아가기",
                // 알림
                "please-enter-data": "원본 주문 데이터를 입력하세요",
                "data-copied": "데이터가 클립보드에 복사되었습니다",
                "edit-enabled": "편집 모드가 활성화되었습니다",
                "changes-saved": "변경사항이 저장되었습니다",
                "multiple-orders-found": "다중 주문이 감지되었습니다, 처리 중...",
                // 필드 값
                "airport-pickup": "공항 픽업",
                "airport-dropoff": "공항 드랍",
                "charter": "전세 서비스"
            }
        };

        // 更新所有UI文本
        function updateUILanguage(lang) {
            // 更新标题
            document.title = `OTA Order Converter - ${i18n[lang]["fliggy-original-order"]}`;
            
            // 更新data-i18n属性的元素
            document.querySelectorAll('[data-i18n]').forEach(element => {
                const key = element.getAttribute('data-i18n');
                if (i18n[lang][key]) {
                    element.textContent = i18n[lang][key];
                }
            });
            
            // 更新占位符
            document.querySelectorAll('[data-i18n-placeholder]').forEach(element => {
                const key = element.getAttribute('data-i18n-placeholder');
                if (i18n[lang][key]) {
                    element.placeholder = i18n[lang][key];
                }
            });
            
            // 保存当前语言到localStorage
            localStorage.setItem('preferred-language', lang);
        }

        // 定义Fliggy的识别规则
        const fliggyRule = {
            id: 1,
            provider: 'Fliggy',
            identification: 'fliggy, 订单编号, 买家, 支付时间',
            fieldMappings: [
                { field: 'reference', pattern: '订单编号：(\\d{19})' },
                { field: 'name', pattern: '买家：([^\\s]+)\\s*支付时间' },
                { field: 'phone', pattern: '真实号：(\\d{11})' },
                { field: 'flight', pattern: '(?:[\\[预计抵达\\]|约[\\d.]+公里|航班号：?)\\s*([A-Z0-9]{2,7})' },
                { field: 'flight-backup', pattern: '\\b([A-Z]{1,3}\\d{1,4}|\\d{1,2}[A-Z]{1,2}\\d{1,4})\\b' },
                { field: 'pickup-datetime', pattern: '(?:\\[预计抵达\\]\\s*|约[\\d.]+公里\\s*)(\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2})' },
                { field: 'pickup-address', pattern: '\\[出发\\](.+)' },
                { field: 'dropoff-address', pattern: '\\[抵达\\](.+)' },
                { field: 'car-type', pattern: '查看详情\\s*\\n\\s*([\\u4e00-\\u9fa5]+\\d+座)' },
                { field: 'passengers', pattern: '(\\d+)成人' },
                { field: 'merchant-received', pattern: '商家实收：(\\d+(?:\\.\\d+)?)元' },
                { field: 'driver-name', pattern: '司机姓名：([^\\n]+)' },
                { field: 'driver-phone', pattern: '司机电话：([^\\n]+)' },
                { field: 'remark', pattern: '商家优惠：(.+)' }
            ]
        };

        // 车型标准映射
        const vehicleStandard = {
            '经济，舒适 五座': '5 seater',
            '经济，舒适 七座': 'SUV',
            '商务七座': 'Serena',
            '豪华七座': 'Alphard/Velfire',
            '商务九座': 'Starex',
            '中巴十五座': 'Van'
        };

        // 翻译缓存
        const translationCache = new Map();
        
        // 本地翻译映射表       
        const localTranslations = {
            // 马来西亚吉隆坡
            '莱恩酒店': 'Sleeping Lion Hotel',
            '吉隆坡武吉免登世民酒店': 'CitizenM Kuala Lumpur Bukit Bintang',
            '吉隆坡帝盛酒店': 'Dorsett Kuala Lumpur',
            '洲际酒店': 'InterContinental Kuala Lumpur',
            '吉隆坡索菲特': 'Sofitel Kuala Lumpur Damansara',
            '吉隆坡雅乐轩': 'Aloft Kuala Lumpur Sentral',
            '吉隆坡福朋喜来登': 'Four Points by Sheraton Kuala Lumpur',
            '吉隆坡双子塔阳光广场酒店': 'KLCC Sunshine Hotel',
            '吉隆坡悦榕庄': 'Banyan Tree Kuala Lumpur',
            '吉隆坡瑞吉酒店': 'The St. Regis Kuala Lumpur',
            '吉隆坡文华东方': 'Mandarin Oriental Kuala Lumpur',
            '吉隆坡盛贸饭店': 'Traders Hotel Kuala Lumpur',
            '吉隆坡大华酒店': 'The Majestic Hotel Kuala Lumpur',
            '吉隆坡希尔顿': 'Hilton Kuala Lumpur',
            '吉隆坡JW万豪': 'JW Marriott Hotel Kuala Lumpur',
            '吉隆坡丽思卡尔顿': 'The Ritz-Carlton Kuala Lumpur',
            '吉隆坡香格里拉': 'Shangri-La Hotel Kuala Lumpur',
            '吉隆坡铂尔曼酒店': 'Pullman Kuala Lumpur City Centre',
            '吉隆坡艾美酒店': 'Le Méridien Kuala Lumpur',
            '吉隆坡威斯汀酒店': 'The Westin Kuala Lumpur',
            '吉隆坡千禧大酒店': 'Grand Millennium Kuala Lumpur',
            '吉隆坡文华东方酒店': 'Mandarin Oriental, Kuala Lumpur',
            '吉隆坡君悦酒店': 'Grand Hyatt Kuala Lumpur',
            '吉隆坡香格里拉大酒店': 'Shangri-La Kuala Lumpur',
            '吉隆坡四季酒店': 'Four Seasons Hotel Kuala Lumpur',
            '吉隆坡丽思卡尔顿酒店': 'The Ritz-Carlton, Kuala Lumpur',
            '吉隆坡JW万豪酒店': 'JW Marriott Kuala Lumpur',
            '吉隆坡威斯汀酒店': 'The Westin Kuala Lumpur',
            '吉隆坡柏威年酒店 (悦榕庄管理)': 'Pavilion Hotel Kuala Lumpur Managed by Banyan Tree',
            '吉隆坡宾乐雅臻选酒店': 'PARKROYAL COLLECTION Kuala Lumpur',
            '吉隆坡市中心普尔曼酒店及公寓': 'Pullman Kuala Lumpur City Centre Hotel & Residences',
            '吉隆坡悦榕庄': 'Banyan Tree Kuala Lumpur',
            '吉隆坡EQ酒店': 'EQ Kuala Lumpur',
            '吉隆坡 W 酒店': 'W Kuala Lumpur',
            '吉隆坡盛贸饭店': 'Traders Hotel, Kuala Lumpur',
            '吉隆坡Impiana KLCC酒店': 'Impiana KLCC Hotel',
            '吉隆坡洲际酒店': 'InterContinental Kuala Lumpur',
            '吉隆坡希尔顿逸林酒店': 'DoubleTree by Hilton Hotel Kuala Lumpur',
            '吉隆坡希尔顿酒店': 'Hilton Kuala Lumpur',
            '吉隆坡艾美酒店': 'Le Méridien Kuala Lumpur',
            '吉隆坡瑞吉酒店': 'The St. Regis Kuala Lumpur',
            '吉隆坡中环广场雅乐轩酒店': 'Aloft Kuala Lumpur Sentral',
            '吉隆坡大华酒店, 傲途格精选': 'The Majestic Hotel Kuala Lumpur, Autograph Collection',
            '吉隆坡条纹酒店, 傲途格精选': 'Hotel Stripes Kuala Lumpur, Autograph Collection',
            '吉隆坡成功时代广场酒店': 'Berjaya Times Square Hotel, Kuala Lumpur',
            '吉隆坡美利亚酒店': 'Melia Kuala Lumpur',
            '吉隆坡帝盛酒店': 'Dorsett Kuala Lumpur',
            '吉隆坡富丽华武吉免登酒店': 'Furama Bukit Bintang',
            '吉隆坡邵氏广场美居酒店': 'Mercure Kuala Lumpur Shaw Parade',
            '吉隆坡市中心诺富特酒店': 'Novotel Kuala Lumpur City Centre',
            '吉隆坡市中心宜必思酒店': 'ibis Kuala Lumpur City Centre',
            '吉隆坡万丽酒店': 'Renaissance Kuala Lumpur Hotel & Convention Centre',
            '吉隆坡帝国喜来登酒店': 'Sheraton Imperial Kuala Lumpur Hotel',
            '吉隆坡双威太子大酒店': 'Sunway Putra Hotel Kuala Lumpur',
            '吉隆坡雅诗阁酒店公寓': 'Ascott Kuala Lumpur',
            '吉隆坡辉盛庭国际公寓': 'Fraser Place Kuala Lumpur',
            '吉隆坡宾乐雅服务式套房酒店': 'PARKROYAL Serviced Suites Kuala Lumpur',
            '武吉免登辉盛凯贝丽酒店': 'Capri by Fraser, Bukit Bintang',
            '吉隆坡源宿酒店': 'Element Kuala Lumpur',
            '吉隆坡孟沙阿丽拉酒店': 'Alila Bangsar Kuala Lumpur',
            '吉隆坡唐人街福朋喜来登酒店': 'Four Points by Sheraton Kuala Lumpur, Chinatown',
            '吉隆坡武吉免登世民酒店': 'citizenM Kuala Lumpur Bukit Bintang',
            '秋杰 - 奥蒙德酒店': 'The Chow Kit - an Ormond Hotel',
            '吉隆坡廓思酒店': 'Corus Hotel Kuala Lumpur',
            '吉隆坡瑞士花园酒店武吉免登': 'Swiss-Garden Hotel Bukit Bintang Kuala Lumpur',
            '吉隆坡市中心旅客之家酒店': 'Travelodge City Centre, Kuala Lumpur',
            '吉隆坡菲斯酒店': 'THE FACE Suites Kuala Lumpur',
            '吉隆坡 Oasia 套房酒店': 'Oasia Suites Kuala Lumpur by Far East Hospitality',
            '吉隆坡市中心智选假日酒店': 'Holiday Inn Express Kuala Lumpur City Centre',
            '吉隆坡逸兰美居酒店公寓': 'Mercure Living Kuala Lumpur Fraser Residence',
            '吉隆坡孟沙温德姆至尊酒店': 'Wyndham Grand Bangsar Kuala Lumpur',
            '吉隆坡谷中城希提特尔酒店': 'Cititel Mid Valley Kuala Lumpur',
            '吉隆坡花园 St Giles 署名酒店及公寓': 'The Gardens – A St Giles Signature Hotel & Residences',
            '吉隆坡皇家朱兰酒店': 'Royale Chulan Kuala Lumpur',
            '吉隆坡凯煌酒店': 'Concorde Hotel Kuala Lumpur',
            '吉隆坡安莎酒店': 'Ansa Hotel Kuala Lumpur',
            '吉隆坡皇家酒店': 'Hotel Royal Kuala Lumpur',
            '吉隆坡日志酒店': 'The Kuala Lumpur Journal Hotel',
            '吉隆坡 KLoé 酒店': 'KLoé Hotel',
            '吉隆坡龙城世家武吉锡兰服务式公寓': 'Lanson Place Bukit Ceylon Serviced Residences',
            '吉隆坡泛太平洋服务套房酒店': 'Pan Pacific Serviced Suites Kuala Lumpur',
            '吉隆坡盛捷服务公寓': 'Somerset Kuala Lumpur',
            '吉隆坡中环雅诗阁服务公寓': 'Ascott Sentral Kuala Lumpur',
            '吉隆坡满家乐凯悦嘉寓酒店': 'Hyatt House Kuala Lumpur, Mont Kiara',
            '吉隆坡白沙罗索菲特酒店': 'Sofitel Kuala Lumpur Damansara',
            '吉隆坡 Jalan TAR North 希尔顿花园酒店': 'Hilton Garden Inn Kuala Lumpur Jalan Tuanku Abdul Rahman North',
            '吉隆坡市中心华美达酒店': 'Ramada by Wyndham Kuala Lumpur City Centre',
            '吉隆坡弗雷泽商业园宜必思尚品酒店': 'ibis Styles Kuala Lumpur Fraser Business Park',
            '吉隆坡AC万豪酒店': 'AC Hotel by Marriott Kuala Lumpur',
            '吉隆坡双威伟乐酒店': 'Sunway Velocity Hotel Kuala Lumpur',
            '天井酒店': 'Tian Jing Hotel',
            '灯笼酒店': 'Lantern Hotel',
            '吉隆坡中央市场太平洋快捷酒店': 'Pacific Express Hotel Central Market Kuala Lumpur',
            '吉隆坡中转酒店': 'Hotel Transit Kuala Lumpur',
            '吉隆坡棉兰端姑普雷斯科特酒店': 'Prescott Hotel Kuala Lumpur Medan Tuanku',
            '吉隆坡PNB派乐纳福朋酒店及套房': 'PNB Perdana Hotel & Suites On The Park Kuala Lumpur',
            '吉隆坡 Expressionz 专业套房': 'Expressionz Professional Suites', 
            '吉隆坡 Ceylonz 套房': 'Ceylonz Suites Bukit Ceylon', 
            '万达镇艾凡特酒店': 'Avante Hotel', 
            '八打灵再也阿玛达酒店': 'Armada Petaling Jaya',

            // 亚庇 - 哥打京那巴鲁
            '亚庇 - 哥打京那巴鲁酒店': 'Apari - Gudang Bala Hotel',
            '绿蔓酒店 – 万豪旅享家设计酒店品牌成员': 'The LUMA Hotel, a Member of Design Hotels',
            '哥打京那巴鲁凯悦尚萃酒店': 'Hyatt Centric Kota Kinabalu',
            '哥打京那巴鲁艾美酒店': 'Le Meridien Kota Kinabalu',
            '哥打京那巴鲁万豪酒店': 'Kota Kinabalu Marriott Hotel',
            '哥打京那巴鲁希尔顿酒店': 'Hilton Kota Kinabalu',
            '哥打京那巴鲁凯悦酒店': 'Hyatt Regency Kinabalu',
            '麦哲伦苏泰拉度假村': 'The Magellan Sutera Resort', 
            '太平洋苏泰拉酒店': 'The Pacific Sutera Hotel', 
            '哥打京那巴鲁香格里拉丹绒亚路酒店': 'Shangri-La Tanjung Aru, Kota Kinabalu', 
            '哥打京那巴鲁香格里拉莎利雅酒店': 'Shangri-La Rasa Ria, Kota Kinabalu', 
            '哥打京那巴鲁市中心美居酒店': 'Mercure Kota Kinabalu City Centre',
            '哥打京那巴鲁市中心智选假日酒店': 'Holiday Inn Express Kota Kinabalu City Centre',
            '宜必思尚品哥打京那巴鲁茵南酒店': 'ibis Styles Kota Kinabalu Inanam', 

            // --- 市中心热门酒店 ---
            '加雅中心酒店': 'Gaya Centre Hotel',
            '豪丽胜酒店': 'Horizon Hotel',
            '格兰迪斯酒店': 'Grandis Hotel',
            '哥打京那巴鲁普罗姆纳德酒店': 'Promenade Hotel Kota Kinabalu',
            'Sixty3酒店': 'Hotel Sixty3',
            '卢玛酒店 - 设计酒店成员': 'The LUMA Hotel, a Member of Design Hotels',
            '哥打京那巴鲁快捷酒店': 'Cititel Express Kota Kinabalu',
            '梦想酒店': 'Dreamtel Kota Kinabalu',
            '哥打京那巴鲁香格里拉酒店': 'Hotel Shangri-la Kota Kinabalu', 
            '明园酒店及公寓': 'Ming Garden Hotel & Residences',
            '斯坦顿酒店': 'Stanton Hotel',
            '哲斯顿酒店': 'The Jesselton Hotel',
            '天空酒店': 'Sky Hotel Kota Kinabalu',
            '沙巴东方酒店': 'Sabah Oriental Hotel',
            '奥舍尼亚酒店': 'Oceania Hotel',
            '京那巴鲁达雅酒店': 'Kinabalu Daya Hotel',
            '京都酒店': 'Hotel Capital',
            '盖亚95酒店': 'Hotel Gaia 95',
            '7 Suria 酒店': 'Hotel 7 Suria',
            '唐朝园酒店': 'Tang Dynasty Park Hotel',
            '胜利酒店': 'Winner Hotel',
            '钻石酒店': 'Diamond Inn',
            '伊甸54号酒店': 'Hotel Eden54',
            '京打公园酒店': 'King Park Hotel Kota Kinabalu',
            '哥打京那巴鲁文华酒店': 'Mandarin Hotel Kota Kinabalu', 
            '杰塞尔顿角酒店': 'Jesselton Point Hotel',
            '阿德尔酒店': 'Adel Hotel',

            // --- 精品酒店 / 服务式公寓 / 其他区域 ---
            'AC公寓': 'AC Residence', 
            '查亚酒店': 'C<haya Hotel',
            'Zara精品酒店@海港城': 'Zara s Boutique Hotel @ Harbour City',
            '摩纳哥精品酒店(Sadong Jaya)': 'Monaco Boutique Hotel Sadong Jaya',
            '拉亚酒店哥打京那巴鲁': 'Raia Hotel Kota Kinabalu', 
            '泛婆罗洲酒店哥打京那巴鲁': 'Pan Borneo Hotel Kota Kinabalu', 
            'The Aru Hotel at Aru Suites': 'The Aru Hotel at Aru Suites', 
            '飞荚酒店': 'FLYPOD Hotel', 
            'Megah D Aru 酒店': 'Megah D Aru Hotel', 
            '艾美纳酒店': 'Eminent Hotel',
            '里卡士广场公寓酒店': 'Likas Square Apartment Hotel', 

            // --- 旅舍 / 背包客栈 / 胶囊旅馆 ---
            'Homy海滨旅舍': 'Homy Seafront Hostel',
            'TOOJOU 哥打京那巴鲁': 'TOOJOU Kota Kinabalu',
            'Escape 背包客栈': 'Escape Backpackers KK',
            '婆罗洲加雅旅馆': 'Borneo Gaya Lodge',
            'Napzone KKIA by Sovotel': 'Napzone KKIA by Sovotel', 

            // --- 稍远度假村 / 特色住宿 ---
            '婆罗洲海滩别墅': 'Borneo Beach Villas', 
            '佳蓝文莱度假村': 'Nexus Resort & Spa Karambunai', 
            '可可山度假村': 'Kokol Haven Resort', 
            '万豪度假会®太平洋苏泰拉酒店': 'Marriott Vacation Club® at The Pacific Sutera', 

            // --- Sutera Avenue / Jesselton Quay 等公寓区常见名称 (由不同房东/公司管理) ---
            // Booking.com上这些名称可能很多，以下为示例，预订时请看清具体房东/管理方
            '苏特拉大道公寓': 'Sutera Avenue Residences', 
            '杰塞尔顿码头海景公寓': 'Jesselton Quay Sea View', 
            '艾玛吉服务套房酒店': 'IMAGO The Official Hotel & Service Suites', 
            '哥打京那巴鲁海景阁楼套房公寓': 'The Loft Residences @ KK Times Square', 
            '滨海庭院度假公寓': 'Marina Court Resort Condominium',


            // 马来西亚仙本那
            '仙本那灯塔酒店': 'Semporna Lighthouse Hotel',
            '仙本那新佳马达潜水度假村': 'Sipadan-Kapalai Dive Resort',
            '仙本那海丰精品大酒店': 'Seafest Boutique Hotel Semporna',
            '仙本那马达京彩瑚度假村': 'Mataking Reef Resort',
            '仙本那马布岛水上屋': 'Mabul Water Bungalows',
            '仙本那卡帕莱度假村': 'Kapalai Dive Resort',
            '仙本那婆罗洲潜水度假村': 'Borneo Divers Mabul Resort',
            '仙本那西巴丹水上屋': 'Sipadan Water Village Resort',
            '仙本那诗巴丹度假村': 'Sipadan Resort',
            '仙本那邦邦岛白珍珠度假村': 'Pompom Island White Pearl Resort',
            '仙本那海丰大酒店': 'Seafest Hotel Semporna',
            '仙本那诗巴丹2号酒店': 'Sipadan Inn 2', 
            '仙本那龙门客栈': 'Dragon Inn Semporna', 
            '仙本那方块床位栈': 'Cube Bed Station', 
            '仙本那Paly酒店': 'Paly Hotel Semporna', 
            '仙本那绿世界酒店': 'Green World Hotel', 
            '仙本那汉宫酒店': 'Han Palace Hotel Semporna', 
            '仙本那海洋旅馆': 'Ocean Inn', 
            '仙本那格雷斯酒店': 'Grace Hotel Semporna', 
            '仙本那记忆精品酒店': 'Memory Boutique Hotel', 
            '仙本那尚思酒店': 'Sense Hotel Semporna', 
            '仙本那遗产酒店': 'Heritage Hotel', 
            '仙本那诗巴丹1号酒店': 'Sipadan Inn 1',
            '仙本那 Ang Lee 假日住宿': 'Ang Lee Holiday Stay', 
            '仙本那城市旅馆': 'City Inn Semporna', 
            '仙本那TD珍珠酒店': 'TD Mutiara Hotel', 
            '仙本那假日潜水客栈': 'Holiday Dive Inn', 
            '仙本那婆罗洲环球诗巴丹背包客栈': 'Borneo Global Sipadan Backpackers', 
            '仙本那 Chan Living': 'Chan Living', 
            '仙本那海洋旅游中心酒店': 'Ocean Tourism Center Hotel', 
            '仙本那My Inn酒店': 'My Inn Hotel Semporna', 
            '仙本那浪景酒店': 'The Wave View Hotel', 
            '仙本那Wai Lai民宿': 'Wai Lai Pension', 
            '仙本那露娜旅馆': 'Luna Guesthouse', 
            '仙本那 Arung Hayat 酒店': 'Arung Hayat Semporna',
            '仙本那 永达大酒店': 'Wing Tat Grand Hotel Semporna',
            
            // 马来西亚槟城
            '槟城香格里拉沙洋度假酒店': 'Shangri-La\'s Rasa Sayang Resort Penang',
            '槟城东方大酒店': 'Eastern & Oriental Hotel Penang',
            '槟城硬石酒店': 'Hard Rock Hotel Penang',
            '槟城G酒店': 'G Hotel Gurney',
            '槟城双威酒店': 'Sunway Hotel Georgetown',
            '槟城宾乐雅酒店': 'Parkroyal Penang Resort',
            '槟城彩虹天堂海滩度假村': 'Rainbow Paradise Beach Resort Penang',
            '槟城皇家酒店': 'Royale Chulan Penang',
            
            // 新加坡
            '新加坡卡尔顿城市酒店': 'Carlton City Hotel Singapore',
            'Novotel Living Singapore Orchard': 'Novotel Living Singapore Orchard',
            'Four Points by Sheraton Singapore, Riverview': 'Four Points by Sheraton Singapore, Riverview',
            '圣淘沙名胜世界-欧芮酒店': 'Hotel Ora Sentosa',
            '新加坡金沙酒店': 'Marina Bay Sands Singapore',
            '新加坡香格里拉': 'Shangri-La Hotel Singapore',
            '新加坡丽思卡尔顿美年': 'The Ritz-Carlton Millenia Singapore',
            '新加坡圣淘沙名胜世界': 'Resorts World Sentosa',
            '新加坡康莱德酒店': 'Conrad Centennial Singapore',
            '新加坡乌节大酒店': 'Orchard Hotel Singapore',
            '新加坡泛太平洋酒店': 'Pan Pacific Singapore',
            '新加坡费尔蒙酒店': 'Fairmont Singapore',
            '新加坡瑞士史丹佛酒店': 'Swissôtel The Stamford Singapore',
            '新加坡滨海湾金沙酒店': 'Marina Bay Sands',
            '新加坡浮尔顿酒店': 'The Fullerton Hotel Singapore',
            '新加坡丽思卡尔顿美年酒店': 'The Ritz-Carlton, Millenia Singapore',
            '新加坡文华东方酒店': 'Mandarin Oriental, Singapore',
            '新加坡泛太平洋酒店': 'Pan Pacific Singapore',
            '新加坡滨海湾宾乐雅臻选酒店': 'PARKROYAL COLLECTION Marina Bay, Singapore',
            '新加坡南岸JW万豪酒店': 'JW Marriott Hotel Singapore South Beach',
            '新加坡香格里拉大酒店': 'Shangri-La Singapore',
            '新加坡四季酒店': 'Four Seasons Hotel Singapore',
            '新加坡乌节希尔顿酒店': 'Hilton Singapore Orchard',
            '新加坡董厦万豪酒店': 'Singapore Marriott Tang Plaza Hotel',
            '新加坡君悦酒店': 'Grand Hyatt Singapore',
            '新加坡乌节路YOTEL酒店': 'YOTEL Singapore Orchard Road',
            '新加坡乌节门JEN酒店 (香格里拉集团)': 'JEN Singapore Orchardgateway by Shangri-La',
            '新加坡史各士皇族酒店': 'Royal Plaza on Scotts',
            '新加坡良木园酒店': 'Goodwood Park Hotel',
            
            // --- 圣淘沙岛 (Sentosa Island) ---
            '新加坡圣淘沙名胜世界 - 逸濠酒店': 'Resorts World Sentosa - Equarius Hotel',
            '新加坡圣淘沙名胜世界 - 迈克尔酒店': 'Resorts World Sentosa - Hotel Michael',
            '新加坡嘉佩乐酒店': 'Capella Singapore',
            '新加坡香格里拉圣淘沙度假酒店': 'Shangri-La Rasa Sentosa, Singapore',
            '新加坡圣淘沙湾 W 酒店': 'W Singapore - Sentosa Cove',
            '新加坡圣淘沙索菲特水疗度假酒店': 'Sofitel Singapore Sentosa Resort & Spa',

            // --- 市政厅 / 克拉码头 / 武吉士 (City Hall / Clarke Quay / Bugis) ---
            '新加坡莱佛士酒店': 'Raffles Hotel Singapore',
            '新加坡史丹福瑞士酒店': 'Swissôtel The Stamford',
            '新加坡费尔蒙酒店': 'Fairmont Singapore',
            '新加坡洲际酒店': 'InterContinental Singapore',
            '新加坡卡尔登酒店': 'Carlton Hotel Singapore',
            '新加坡克拉码头百乐商业酒店': 'Paradox Singapore Merchant Court at Clarke Quay',

            // --- 其他区域 (Chinatown / Downtown etc.) ---
            '新加坡皮克林宾乐雅臻选酒店': 'PARKROYAL COLLECTION Pickering, Singapore',
            '新加坡市中心奥西亚酒店 (远东酒店集团)': 'Oasia Hotel Downtown, Singapore by Far East Hospitality',
            '新加坡市中豪亚酒店 (远东酒店集团)': 'The Clan Hotel Singapore by Far East Hospitality'


        };

        // 本地模糊匹配函数
        function fuzzyMatchLocal(text) {
            // 精确匹配
            if (localTranslations[text]) {
                return localTranslations[text];
            }
            
            // 包含匹配
            for (const [key, value] of Object.entries(localTranslations)) {
                if (text.includes(key)) {
                    return text.replace(key, value);
                }
            }
            
            // 反向包含匹配（文本是键的一部分）
            for (const [key, value] of Object.entries(localTranslations)) {
                if (key.includes(text)) {
                    return value;
                }
            }
            
            return null;
        }

        // 修改后的翻译函数
        async function translateWithCache(text) {
            if (!text || text.trim() === '') return text;
            
            // 1. 优先检查缓存
            if (translationCache.has(text)) {
                return translationCache.get(text);
            }
            
            // 2. 尝试本地模糊匹配
            const localMatch = fuzzyMatchLocal(text);
            if (localMatch) {
                translationCache.set(text, localMatch);
                return localMatch;
            }
            
            // 3. 尝试使用 Gemini API 查询
            try {
                console.log(`本地翻译未找到匹配，尝试使用 Gemini API 查询: ${text}`);
                const geminiResult = await queryPOIEnglishName(text);
                
                if (geminiResult && geminiResult !== text) {
                    console.log(`Gemini API 查询成功: ${text} -> ${geminiResult}`);
                    translationCache.set(text, geminiResult);
                    return geminiResult;
                }
            } catch (error) {
                console.error(`Gemini API 查询失败: ${error.message}`);
            }
            
            // 4. 所有方法都失败，返回原文
            return text;
        }

        // DOM元素引用
        const rawOrderTextarea = document.getElementById('raw-order');
        const convertBtn = document.getElementById('convert-btn');
        const resetBtn = document.getElementById('reset-btn');
        const editBtn = document.getElementById('edit-btn');
        const saveBtn = document.getElementById('save-btn');
        const copyOutputBtn = document.getElementById('copy-output');
        const formInputs = document.querySelectorAll('#order-form input, #order-form select, #order-form textarea');
        const multiOrdersContainer = document.getElementById('multi-orders-container');
        const ordersList = document.getElementById('orders-list');
        const notification = document.getElementById('notification');
        const languageSelector = document.getElementById('language-selector');

        // 转换订单主函数
        function convertOrder() {
            // 获取原始订单文本
            const rawOrderText = rawOrderTextarea.value.trim();
            
            if (!rawOrderText) {
                const currentLang = languageSelector.value;
                showNotification(i18n[currentLang]['please-enter-data']);
                return;
            }
            
            // 检查是否有多个订单
            const orderRegex = /订单编号：\d{19}/g;
            const orderMatches = [...rawOrderText.matchAll(orderRegex)];
            
            if (orderMatches.length > 1) {
                // 多订单处理
                processMultipleOrders(rawOrderText);
                return;
            }
            
            // 单订单处理
            processSingleOrder(rawOrderText);
        }

        // 处理单个订单
        async function processSingleOrder(rawOrderText) {
            // 显示标准订单表单，隐藏多订单容器
            document.querySelector('.grid').classList.remove('hidden');
            multiOrdersContainer.classList.add('hidden');
            
            // 提取订单数据
            const orderData = extractOrderData(rawOrderText);
            
            // 确定订单类型和驾驶区域
            determineOrderType(rawOrderText, orderData);
            determineDrivingRegion(rawOrderText, orderData);
            
            // 处理pickup和dropoff地址翻译
            orderData['pickup-address'] = await processAddress(orderData['pickup-address']);
            orderData['dropoff-address'] = await processAddress(orderData['dropoff-address']);
            
            // 填充表单
            fillOrderForm(orderData);
        }

        // 处理多个订单
        async function processMultipleOrders(rawOrderText) {
            // 隐藏标准订单表单，显示多订单容器
            document.querySelector('.grid').classList.add('hidden');
            multiOrdersContainer.classList.remove('hidden');
            
            // 清空订单列表
            ordersList.innerHTML = '';
            
            // 分割多个订单
            const orders = splitOrders(rawOrderText);
            
            // 处理每个订单
            for (let i = 0; i < orders.length; i++) {
                const orderText = orders[i];
                const orderData = extractOrderData(orderText);
                
                // 确定订单类型和驾驶区域
                determineOrderType(orderText, orderData);
                determineDrivingRegion(orderText, orderData);
                
                // 处理地址翻译
                orderData['pickup-address'] = await processAddress(orderData['pickup-address']);
                orderData['dropoff-address'] = await processAddress(orderData['dropoff-address']);
                
                // 标记地址已翻译
                orderData['pickup-address-translated'] = true;
                orderData['dropoff-address-translated'] = true;
                
                // 确保使用vehicle-type作为主要车型字段
                if (orderData['vehicle-type'] && !orderData['car-type']) {
                    orderData['car-type'] = orderData['vehicle-type'];
                } else if (orderData['car-type'] && !orderData['vehicle-type']) {
                    orderData['vehicle-type'] = orderData['car-type'];
                }
                
                // 创建并添加订单元素
                const orderElement = createOrderElement(orderData, orderText, i + 1);
                ordersList.appendChild(orderElement);
            }
        }// 在fliggy.html的<script>标签内添加（建议位置：1620行附近，现有函数之前）
        // ============== 新增独立识别模块开始 ==============
        /**
         * 增强订单识别模块（完全独立）
         * 功能：处理自由格式订单的识别，不依赖现有解析逻辑
         */
        const OrderPatternEnhancer = (function() {
          // 私有匹配规则
          const _patterns = {
            date: [
              { regex: /(\d{4})年(\d{1,2})月(\d{1,2})日/, handler: (_,y,m,d) => `${y}-${m}-${d}` },
              { regex: /(\d{1,2})[月\-\.](\d{1,2})[日号]?/, handler: (_,m,d) => `2025-${m.padStart(2,'0')}-${d.padStart(2,'0')}` }
            ],
            time: /(\d{1,2})[:：]?(\d{2})?\s*(上午|下午)?/,
            people: /(\d+)[大人小]/,
            luggage: {
              sizes: /(\d+寸)[^+]*(\d+)?/g,
              types: /(托运|登机)[^:：]*(\d+)/
            },
            vehicle: /(\d+座)[车輛]?/
          };
        
          // 私有辅助方法
          function _parseMatched(match, handler) {
            return match ? handler(...match) : null;
          }
        
          // 公开API
          return {
            /**
             * 识别自由格式订单
             * @param {string} text 订单文本
             * @returns {object|null} 识别结果或null（当不符合自由格式时）
             */
            parse: function(text) {
              if (text.includes(':') || text.includes('：')) return null; // 排除标签格式
        
              const result = { 
                metadata: { format: 'free' },
                recognizedFields: []
              };
        
              // 日期解析
              for (const format of _patterns.date) {
                const match = text.match(format.regex);
                if (match) {
                  result.date = _parseMatched(match, format.handler);
                  result.recognizedFields.push('date');
                  break;
                }
              }
        
              // 其他字段解析（示例）
              ['time', 'people', 'vehicle'].forEach(field => {
                const match = text.match(_patterns[field]);
                if (match) {
                  result[field] = match[1];
                  result.recognizedFields.push(field);
                }
              });
        
              return result.recognizedFields.length > 0 ? result : null;
            }
          };
        })();
        // ============== 新增独立识别模块结束 ==============

        // 分割多个订单
        function splitOrders(rawOrderText) {
            // 使用"订单编号："作为分割点
            const orderRegex = /(订单编号：\d{19}[\s\S]*?)(?=订单编号：\d{19}|$)/g;
            const orders = [];
            let match;
            
            while ((match = orderRegex.exec(rawOrderText)) !== null) {
                orders.push(match[1].trim());
            }
            
            return orders;
        }

        // 提取订单数据
        function extractOrderData(orderText) {
            const orderData = {
                ota: 'Fliggy',
                language: 'Chinese',
                driver: 1,
                category: '',
                subcategory: ''
            };
            
            // 根据规则映射提取字段
            for (const mapping of fliggyRule.fieldMappings) {
                try {
                    const regex = new RegExp(mapping.pattern, 'i');
                    const match = orderText.match(regex);
                    
                    if (match && match[1]) {
                        const fieldId = mapping.field;
                        orderData[fieldId] = match[1].trim();
                    }
                } catch (e) {
                    console.error(`提取字段 ${mapping.field} 时出错:`, e);
                }
            }
            
            // 车型识别逻辑（档次+座位模式）
            const lowerOrderText = orderText.toLowerCase();
            console.log('开始车型识别，订单文本:', lowerOrderText);
            
            // 1. 识别车辆档次
            const vehicleClass = 
                lowerOrderText.includes('豪华') ? '豪华' :
                lowerOrderText.includes('商务') ? '商务' :
                lowerOrderText.includes('经济') || lowerOrderText.includes('舒适') ? '经济' : '';
            
            // 2. 识别座位数
            const seatPatterns = [
                { pattern: /(?:五座|5座|5人座|V座|舒适5座)/, seats: '五座' },
                { pattern: /(?:七座|7座|7人座|VII座|舒适7座)/, seats: '七座' },
                { pattern: /(?:九座|9座|9人座|IX座)/, seats: '九座' },
                { pattern: /(?:十五座|15座|18座|XV座|14座中巴)/, seats: '十五座' }
            ];
            
            let seatType = '';
            for (const {pattern, seats} of seatPatterns) {
                if (pattern.test(lowerOrderText)) {
                    seatType = seats;
                    break;
                }
            }
            
            // 3. 组合判断
            if (seatType === '十五座' || lowerOrderText.includes('中巴')) {
                // 中巴类特殊处理
                orderData['vehicle-type'] = 'Van';
                console.log('中巴车型识别: Van');
            }
            else if (vehicleClass && seatType) {
                const key = `${vehicleClass}${seatType}`;
                const standardMapping = {
                    '经济五座': 'sedan',
                    '经济七座': 'SUV',
                    '商务七座': 'Serena',
                    '豪华七座': 'Alphard',
                    '商务九座': 'Starex'
                };
                
                if (standardMapping[key]) {
                    console.log('识别到车型:', key, '=>', standardMapping[key]);
                    orderData['vehicle-type'] = standardMapping[key];
                } else {
                    console.log('未配置的车型组合:', key);
                }
            } else {
                console.log('车型识别失败:', 
                    `档次=${vehicleClass || '未识别'}, ` +
                    `座位=${seatType || '未识别'}`);
            }
            
            // 将reference映射到ota-reference
            if (orderData.reference) {
                orderData['ota-reference'] = orderData.reference;
            }
            
            // 将merchant-received映射到price，并根据区域计算价格
            if (orderData['merchant-received']) {
                const price = parseFloat(orderData['merchant-received']);
                if (!isNaN(price)) {
                    // 基础费率
                    const baseRate = 0.84;
                    // 分别识别两国关键词
                    const hasMalaysia = /马来西亚[\-－]?吉隆坡|malaysia|kl|penang|johor|sabah|吉隆坡|槟城|柔佛|沙巴/i.test(orderText);
                    const hasSingapore = /新加坡|singapore|sg|狮城/i.test(orderText);
                    const regionRate = hasSingapore ? 0.18 : hasMalaysia ? 0.61 : 0.61;
                    orderData.price = (price * baseRate * regionRate).toFixed(2);
                }
            }
            
            // 处理航班号：优先使用主要提取规则，如果失败则使用备用规则
            if (orderData.flight) {
                orderData['flight-number'] = orderData.flight;
            } else if (orderData['flight-backup']) {
                orderData['flight-number'] = orderData['flight-backup'];
            } else {
                // 直接从原始文本中搜索可能的航班号格式
                const flightNumberPatterns = [
                    /\b([A-Z]{2}\d{1,4})\b/,      // 标准航班号：两个字母+数字（如CX123）
                    /\b([A-Z]{3}\d{1,4})\b/,      // 三字母代码+数字（如CPA123）
                    /\b(\d{1,2}[A-Z]{1,2}\d{1,4})\b/, // 数字+字母+数字（如3K536, 9W123）
                    /\b([A-Z]\d{3,4})\b/,         // 单字母+数字（如G5123）
                    /\b([A-Z]{2}\d{1,3}[A-Z])\b/  // 字母+数字+字母（如MU123A）
                ];
                
                for (const pattern of flightNumberPatterns) {
                    const match = orderText.match(pattern);
                    if (match && match[1]) {
                        orderData['flight-number'] = match[1];
                        break;
                    }
                }
            }
            
            // 清理无用的临时字段
            delete orderData.flight;
            delete orderData['flight-backup'];
            
            // 将passengers映射到passenger-number
            if (orderData.passengers) {
                orderData['passenger-number'] = orderData.passengers;
            }
            
            // 判断是接机还是送机
            determineOrderType(orderText, orderData);
            
            // 判断驾驶区域
            determineDrivingRegion(orderText, orderData);
            
            return orderData;
        }

        // 判断订单类型 (接机、送机或包车)
        function determineOrderType(orderText, orderData) {
            // 关键词检测
            const pickupKeywords = ['接机', '接送机', '接车', '机场接', '接送服务', 'airport pickup', 'pick up', 'pick-up', 'arrival', 'meet and greet'];
            const dropoffKeywords = ['送机', '去机场', '机场送', '送往机场', 'airport drop-off', 'drop off', 'departure', 'send to airport'];
            
            const lowerOrderText = orderText.toLowerCase();
            const hasPickupKeywords = pickupKeywords.some(keyword => 
                lowerOrderText.includes(keyword.toLowerCase())
            );
            const hasDropoffKeywords = dropoffKeywords.some(keyword => 
                lowerOrderText.includes(keyword.toLowerCase())
            );
            
            // 判断是否是机场相关订单
            const airportKeywords = ['机场', '航站', '航厦', '空港', 'airport', 'terminal'];
            const hasAirportKeywords = airportKeywords.some(keyword => 
                lowerOrderText.includes(keyword.toLowerCase())
            );
            
            if (hasAirportKeywords || hasPickupKeywords || hasDropoffKeywords) {
                orderData.category = 'airport';
                
                if (hasPickupKeywords && !hasDropoffKeywords) {
                    orderData.subcategory = 'pickup';
                } else if (hasDropoffKeywords && !hasPickupKeywords) {
                    orderData.subcategory = 'dropoff';
                } else {
                    // 如果两种关键词都有或都没有，根据地址判断
                    const pickupHasAirport = airportKeywords.some(keyword => 
                        (orderData['pickup-address'] || '').toLowerCase().includes(keyword.toLowerCase())
                    );
                    const dropoffHasAirport = airportKeywords.some(keyword => 
                        (orderData['dropoff-address'] || '').toLowerCase().includes(keyword.toLowerCase())
                    );
                    
                    if (pickupHasAirport) {
                        orderData.subcategory = 'pickup';
                    } else if (dropoffHasAirport) {
                        orderData.subcategory = 'dropoff';
                    } else {
                        orderData.subcategory = 'pickup'; // 默认为接机
                    }
                }
            }
            else {
                orderData.category = 'charter';
                orderData.subcategory = '';
            }
        }

        // 判断驾驶区域
        function determineDrivingRegion(orderText, orderData) {
            const regionMapping = [
                { code: 'kl', keywords: ['kuala lumpur', 'kl', 'selangor', '吉隆坡', '雪兰莪'] },
                { code: 'penang', keywords: ['penang', '槟城'] },
                { code: 'jb', keywords: ['johor', 'jb', '柔佛', '新山'] }, 
                { code: 'sabah', keywords: ['sabah', '沙巴', '亚庇', 'kota kinabalu', '仙本那'] },
                { code: 'sg', keywords: ['singapore', 'changi', '新加坡', '樟宜机场'] }
            ];
            
            const lowerOrderText = orderText.toLowerCase();
            let regionChanged = false;
            
            // 查找匹配的驾驶区域
            for (const region of regionMapping) {
                if (region.keywords.some(keyword => lowerOrderText.includes(keyword))) {
                    regionChanged = orderData['driving-region'] !== region.code;
                    orderData['driving-region'] = region.code;
                    return;
                }
            }
            
            // 默认设置为吉隆坡
            orderData['driving-region'] = 'kl';
        }

        // 处理中文地址中的POI翻译
        async function processAddress(address) {
            if (!address) return address;
            
            try {
                // 计算字符串相似度 (Levenshtein距离)的函数
                function calculateStringSimilarity(str1, str2) {
                    if (!str1 || !str2) return 0;
                    
                    // 创建矩阵
                    const matrix = Array(str1.length + 1).fill().map(() => Array(str2.length + 1).fill(0));
                    
                    // 初始化矩阵
                    for (let i = 0; i <= str1.length; i++) {
                        matrix[i][0] = i;
                    }
                    for (let j = 0; j <= str2.length; j++) {
                        matrix[0][j] = j;
                    }
                    
                    // 填充矩阵
                    for (let i = 1; i <= str1.length; i++) {
                        for (let j = 1; j <= str2.length; j++) {
                            const cost = str1[i - 1] === str2[j - 1] ? 0 : 1;
                            matrix[i][j] = Math.min(
                                matrix[i - 1][j] + 1,     // 删除
                                matrix[i][j - 1] + 1,     // 插入
                                matrix[i - 1][j - 1] + cost // 替换
                            );
                        }
                    }
                    
                    // 计算相似度百分比
                    const maxLength = Math.max(str1.length, str2.length);
                    const distance = matrix[str1.length][str2.length];
                    const similarity = ((maxLength - distance) / maxLength) * 100;
                    
                    return similarity;
                }
                
                // 将地址标准化（去除空格和标点）用于模糊匹配
                function normalizeString(str) {
                    if (!str) return '';
                    return str.toLowerCase().replace(/\s+/g, '').replace(/[.,\/#!$%\^&\*;:{}=\-_`~()]/g, '');
                }
                
                // 步骤1: 检查机场关键词映射
                const airportKeywords = {
                    '樟宜机场': 'Changi Airport',
                    '新加坡樟宜': 'Changi Airport',
                    '吉隆坡机场': 'Kuala Lumpur International Airport',
                    '吉隆坡国际机场': 'Kuala Lumpur International Airport',
                    '吉隆坡国际机场1': 'Kuala Lumpur International Airport Terminal 1',
                    '吉隆坡国际机场2': 'Kuala Lumpur International Airport Terminal 2',
                    '吉隆坡国际机场t1': 'Kuala Lumpur International Airport Terminal 1',
                    '吉隆坡国际机场t2': 'Kuala Lumpur International Airport Terminal 2',
                    '吉隆坡国际机场T1': 'Kuala Lumpur International Airport Terminal 1',
                    '吉隆坡国际机场T2': 'Kuala Lumpur International Airport Terminal 2',
                    '吉隆坡T1': 'Kuala Lumpur International Airport Terminal 1',
                    '吉隆坡T2': 'Kuala Lumpur International Airport Terminal 2',
                    'KLIA': 'Kuala Lumpur International Airport',
                    'KLIA1': 'Kuala Lumpur International Airport Terminal 1',
                    'KLIA2': 'Kuala Lumpur International Airport Terminal 2',
                    'KLIAT1': 'Kuala Lumpur International Airport Terminal 1',
                    'KLIAT2': 'Kuala Lumpur International Airport Terminal 2',
                    'KLIA-T1': 'Kuala Lumpur International Airport Terminal 1',
                    'KLIA-T2': 'Kuala Lumpur International Airport Terminal 2',
                    '斗湖机场': 'Tawau Airport',
                    '斗湖国际': 'Tawau Airport',
                    '亚庇机场': 'Kota Kinabalu International Airport',
                    '亚庇国际': 'Kota Kinabalu International Airport',
                    '槟城机场': 'Penang International Airport',
                    '槟城国际': 'Penang International Airport',
                    '新山机场': 'Senai International Airport',
                    '新山国际': 'Senai International Airport',
                    '沙巴机场': 'Kota Kinabalu International Airport',
                    '沙巴国际': 'Kota Kinabalu International Airport',
                    '仙本那机场': 'Semporna Airport',
                    '新加坡机场': 'Changi International Airport',
                    'klia': 'Kuala Lumpur International Airport',
                    'changi': 'Changi Airport',
                    '浮罗交怡机场': 'Langkawi International Airport',
                    '兰卡威机场': 'Langkawi International Airport',
                    '兰卡威国际': 'Langkawi International Airport',
                    '浮罗交怡国际': 'Langkawi International Airport',
                    '马六甲机场': 'Malacca International Airport',
                    '马六甲国际': 'Malacca International Airport',
                    '亚罗士打机场': 'Alor Setar Airport',
                    '古晋机场': 'Kuching International Airport',
                    '古晋国际': 'Kuching International Airport',
                    '民都鲁机场': 'Bintulu Airport',
                    '诗巫机场': 'Sibu Airport',
                    '马六甲机场': 'Melaka Airport',
                };
                
                // 步骤2: 使用精确匹配+模糊匹配检查是否包含机场关键词
                // 精确匹配
                for (const [keyword, translation] of Object.entries(airportKeywords)) {
                    if (address.includes(keyword)) {
                        console.log(`精确匹配机场关键词: ${keyword} -> ${translation}`);
                        return address.replace(keyword, translation);
                    }
                }
                
                // 模糊匹配
                let bestMatch = null;
                let bestMatchSimilarity = 0;
                let bestMatchKeyword = '';
                const normalizedAddress = normalizeString(address);
                
                // 对地址进行分词并与机场关键词比较相似度
                const addressTokens = normalizedAddress.split(/[\s,\-\/\(\)\[\]]/g).filter(Boolean);
                // 提取可能的机场名称部分
                const possibleAirportPattern = /([^\s]+(?:机场|国际|航站|T\d|t\d|1|2))/gi;
                const possibleAirportMatches = [...address.matchAll(possibleAirportPattern)].map(match => match[1]);
                
                // 合并地址分词和可能的机场名称
                const candidateParts = [...new Set([...addressTokens, ...possibleAirportMatches])];
                
                for (const [keyword, translation] of Object.entries(airportKeywords)) {
                    const normalizedKeyword = normalizeString(keyword);
                    
                    // 检查整个地址的相似度
                    const addressSimilarity = calculateStringSimilarity(normalizedAddress, normalizedKeyword);
                    if (addressSimilarity >= 80 && addressSimilarity > bestMatchSimilarity) {
                        bestMatchSimilarity = addressSimilarity;
                        bestMatch = translation;
                        bestMatchKeyword = keyword;
                    }
                    
                    // 检查各部分的相似度
                    for (const part of candidateParts) {
                        const similarity = calculateStringSimilarity(part, normalizedKeyword);
                        if (similarity >= 80 && similarity > bestMatchSimilarity) {
                            bestMatchSimilarity = similarity;
                            bestMatch = translation;
                            bestMatchKeyword = keyword;
                        }
                    }
                    
                    // 检查关键词是否部分包含在地址中（适用于长地址）
                    if (normalizedKeyword.length > 3) {
                        if (normalizedAddress.includes(normalizedKeyword)) {
                            const similarityScore = 90; // 给予高相似度
                            if (similarityScore > bestMatchSimilarity) {
                                bestMatchSimilarity = similarityScore;
                                bestMatch = translation;
                                bestMatchKeyword = keyword;
                            }
                        }
                    }
                }
                
                // 如果找到模糊匹配且相似度超过80%
                if (bestMatch) {
                    console.log(`模糊匹配机场关键词 (${bestMatchSimilarity.toFixed(2)}% 相似): ${bestMatchKeyword} -> ${bestMatch}`);
                    
                    // 更智能地尝试替换原始地址中与关键词相似的部分
                    const regex = new RegExp(`\\b[\\w\\u4e00-\\u9fa5]+(?:机场|国际机场|国际|航站|T\\d|t\\d|1|2)\\b`, 'gi');
                    let replaced = false;
                    const result = address.replace(regex, (match) => {
                        const matchSimilarity = calculateStringSimilarity(normalizeString(match), normalizeString(bestMatchKeyword));
                        if (matchSimilarity >= 75) { // 略微降低替换阈值，提高匹配几率
                            replaced = true;
                            return bestMatch;
                        }
                        return match;
                    });
                    
                    if (replaced) {
                        return result;
                    }
                    
                    // 如果无法通过正则表达式找到匹配部分
                    // 尝试直接替换最相似的部分
                    if (bestMatchKeyword.length > 2) {
                        for (const part of possibleAirportMatches) {
                            const similarity = calculateStringSimilarity(normalizeString(part), normalizeString(bestMatchKeyword));
                            if (similarity >= 75) {
                                return address.replace(part, bestMatch);
                            }
                        }
                    }
                    
                    // 如果仍然无法替换，添加翻译
                    return `${address} (${bestMatch})`;
                }
                
                // 步骤3: 通用机场关键词检测
                const genericAirportKeywords = ['机场', '航站楼', '航站', '航厦', '国际机场', 'airport', 'terminal', 'international'];
                let containsAirportKeyword = false;
                
                for (const keyword of genericAirportKeywords) {
                    if (address.toLowerCase().includes(keyword.toLowerCase())) {
                        containsAirportKeyword = true;
                        console.log(`检测到通用机场关键词: ${keyword}`);
                        break;
                    }
                }
                
                // 如果包含机场关键词，尝试在本地映射中查找
                if (containsAirportKeyword) {
                    // 首先检查是否整个地址可以在本地映射中找到
                    for (const [key, value] of Object.entries(localTranslations)) {
                        if (address.includes(key)) {
                            console.log(`在本地映射中找到匹配项，跳过API翻译: ${key} -> ${value}`);
                            return address.replace(key, value);
                        }
                    }
                    
                    // 检查英文机场关键词
                    if (/airport|terminal|international/i.test(address)) {
                        console.log(`地址已包含英文机场关键词，跳过翻译: ${address}`);
                        return address;
                    }
                    
                    // 尝试提取城市名并进行模糊匹配
                    const cityMatch = address.match(/^([^机场航站]+)/);
                    if (cityMatch && cityMatch[1]) {
                        const cityName = cityMatch[1];
                        // 简单的城市名映射
                        const cityMapping = {
                            '吉隆坡': 'Kuala Lumpur',
                            '新加坡': 'Singapore',
                            '亚庇': 'Kota Kinabalu',
                            '沙巴': 'Sabah',
                            '槟城': 'Penang',
                            '新山': 'Johor Bahru',
                            '仙本那': 'Semporna'
                        };
                        
                        // 模糊匹配城市名
                        let bestCityMatch = null;
                        let bestCitySimilarity = 0;
                        
                        for (const [city, translation] of Object.entries(cityMapping)) {
                            const similarity = calculateStringSimilarity(normalizeString(cityName), normalizeString(city));
                            if (similarity >= 80 && similarity > bestCitySimilarity) {
                                bestCitySimilarity = similarity;
                                bestCityMatch = translation;
                            }
                        }
                        
                        if (bestCityMatch) {
                            console.log(`模糊匹配城市名 (${bestCitySimilarity.toFixed(2)}% 相似): ${cityName} -> ${bestCityMatch}`);
                            
                            const airportName = address.match(/([^\s]+(?:机场|航站楼|航站|航厦|国际机场))/);
                            if (airportName && airportName[1]) {
                                const englishAirportName = `${bestCityMatch} International Airport`;
                                console.log(`构建机场名称: ${airportName[1]} -> ${englishAirportName}`);
                                return address.replace(airportName[1], englishAirportName);
                            }
                        }
                    }
                    
                    // 如果包含机场关键词但无法精确匹配，尝试构建通用机场名称
                    const genericAirportPattern = /([^\s]+(?:机场|航站楼|航站|航厦|国际机场|机场\d|terminal|航空站|空港|飞行场|飞机场|候机楼|登机口|接机口|出发厅|到达厅|货运站|停机坪|跑道|塔台|航空枢纽|航空港|航空中心|航空基地|航空站|航空设施|航空区域|航空终端|airport|terminal|airfield|aerodrome|airbase|air hub|aviation center|runway|control tower|hangar|cargo terminal|boarding gate|arrival hall|departure hall|baggage claim))/i;
                    const airportMatch = address.match(genericAirportPattern);
                    
                    if (airportMatch && airportMatch[1]) {
                        const airportName = airportMatch[1];
                        
                        // 提取城市名（假设机场前面是城市名）
                        const cityMatch = airportName.match(/^([^机场航站]+)/);
                        if (cityMatch && cityMatch[1]) {
                            const cityName = cityMatch[1];
                            // 尝试在本地映射中查找城市名
                            for (const [key, value] of Object.entries(localTranslations)) {
                                if (key.includes(cityName)) {
                                    // 提取可能的城市英文名
                                    const cityEnglishName = value.split(' ')[0];
                                    if (cityEnglishName) {
                                        const englishAirportName = `${cityEnglishName} International Airport`;
                                        console.log(`从本地映射构建机场名称: ${airportName} -> ${englishAirportName}`);
                                        return address.replace(airportName, englishAirportName);
                                    }
                                }
                            }
                        }
                        
                        // 若无法从本地映射构建，则保留原地址
                        console.log(`无法从本地映射构建机场名称，保留原地址: ${address}`);
                        return address;
                    }
                }
                
                // 步骤4: 如果不包含机场关键词或无法在本地映射中找到，则尝试在本地映射表中查找
                for (const [key, value] of Object.entries(localTranslations)) {
                    if (address.includes(key)) {
                        console.log(`在本地映射中找到匹配项: ${key} -> ${value}`);
                        return address.replace(key, value);
                    }
                }
                
                // 步骤5: 以下是原有的API翻译逻辑，如果没有本地映射匹配项
                // 先尝试直接翻译整个地址
                const directTranslation = await translateWithCache(address);
                if (directTranslation !== address) {
                    console.log(`整体地址翻译成功: ${address} -> ${directTranslation}`);
                    return directTranslation;
                }
                
                // 如果整体翻译失败，尝试提取POI名称
                // 1. 常见场所类型模式
                const poiTypes = ['小区','大厦','广场','中心','酒店','餐厅','商场','医院','学校','机场','车站','公馆','花园','山庄','小镇','公园','大厦','大厅','大厢','市场','广场','店','楼','宫','街','庄','山'];
                const poiRegex = new RegExp(`(.+?(?:${poiTypes.join('|')}))`); 
                const match = address.match(poiRegex);
                
                if (match && match[1]) {
                    const translated = await translateWithCache(match[1]);
                    
                    if (translated !== match[1]) {
                        // 保留地址中的路名/门牌号等非POI部分
                        const nonPoiParts = address.split(poiRegex).filter(Boolean);
                        const translatedParts = nonPoiParts.map(part => 
                            part === match[1] ? translated : part
                        );
                        
                        console.log(`POI匹配翻译成功: ${match[1]} -> ${translated}`);
                        return translatedParts.join('');
                    }
                }
                
                // 2. 尝试将地址按空格或特殊字符分割，逐个翻译
                const addressSegments = address.split(/[\s,\-\/\(\)\[\]]/g).filter(Boolean);
                if (addressSegments.length > 1) {
                    const translatedParts = [];
                    let hasTranslation = false;
                    
                    for (const part of addressSegments) {
                        if (part.length > 1) { // 忽略单个字符
                            const translatedPart = await translateWithCache(part);
                            translatedParts.push(translatedPart !== part ? translatedPart : part);
                            if (translatedPart !== part) hasTranslation = true;
                        } else {
                            translatedParts.push(part);
                        }
                    }
                    
                    if (hasTranslation) {
                        console.log(`分段翻译成功: ${address} -> ${translatedParts.join(' ')}`);
                        return translatedParts.join(' ');
                    }
                }
                
                // 3. 如果上述方法都失败，尝试使用Gemini API直接查询
                // 如果包含机场关键词但API翻译都失败，则返回原始地址，避免不必要的API调用
                if (containsAirportKeyword) {
                    console.log(`地址包含机场关键词但无法翻译，跳过Gemini API调用: ${address}`);
                    return address;
                }
                
                console.log(`尝试使用Gemini API翻译地址: ${address}`);
                const geminiResult = await queryPOIEnglishName(address);
                if (geminiResult && geminiResult !== address) {
                    console.log(`Gemini API地址翻译成功: ${address} -> ${geminiResult}`);
                    return geminiResult;
                }
                
                return address;
                
            } catch (error) {
                console.error('地址翻译异常:', error);
                return address;
            }
        }

        // 填充订单表单
        async function fillOrderForm(orderData) {
            // 车型字段优先使用vehicle-type，其次car-type
            const carTypeValue = orderData['vehicle-type'] || orderData['car-type'] || '';
            document.getElementById('car-type').value = carTypeValue;
            
            // 确保地址字段正确显示翻译后的内容
            const pickupAddress = orderData['pickup-address'] || '';
            const dropoffAddress = orderData['dropoff-address'] || '';
            
            // 输出调试信息
            console.log('填充表单中的地址信息:');
            console.log('接机地址:', pickupAddress);
            console.log('送机地址:', dropoffAddress);
            
            // 其他字段保持不变...
            document.getElementById('ota').value = orderData.ota || '';
            document.getElementById('ota-reference').value = orderData['ota-reference'] || '';
            document.getElementById('price').value = orderData.price || '';
            document.getElementById('name').value = orderData.name || '';
            document.getElementById('phone').value = orderData.phone || '';
            document.getElementById('email').value = orderData.email || '';
            document.getElementById('flight-number').value = orderData['flight-number'] || '';
            document.getElementById('pickup-datetime').value = orderData['pickup-datetime'] || '';
            
            // 强制更新地址字段
            const pickupAddressInput = document.getElementById('pickup-address');
            pickupAddressInput.value = pickupAddress;
            // 触发输入事件以确保值被正确应用
            pickupAddressInput.dispatchEvent(new Event('input', { bubbles: true }));
            
            const dropoffAddressInput = document.getElementById('dropoff-address');
            dropoffAddressInput.value = dropoffAddress;
            dropoffAddressInput.dispatchEvent(new Event('input', { bubbles: true }));
            
            document.getElementById('passenger-number').value = orderData['passenger-number'] || '';
            document.getElementById('luggage-number').value = orderData['luggage-number'] || '';
            document.getElementById('language').value = orderData.language || '';
            // 处理category选择框
            const categorySelect = document.getElementById('category');
            const categoryWasDisabled = categorySelect.disabled;
            categorySelect.disabled = false;
            categorySelect.value = orderData.category || '';
            categorySelect.dispatchEvent(new Event('change', { bubbles: true }));
            categorySelect.disabled = categoryWasDisabled;
            
            // 处理subcategory选择框
            const subcategorySelect = document.getElementById('subcategory');
            const subcategoryWasDisabled = subcategorySelect.disabled;
            subcategorySelect.disabled = false;
            subcategorySelect.value = orderData.subcategory || '';
            subcategorySelect.dispatchEvent(new Event('change', { bubbles: true }));
            subcategorySelect.disabled = subcategoryWasDisabled;
            // 处理驾驶区域选择框
            const drivingRegionSelect = document.getElementById('driving-region');
            // 临时移除disabled属性以便设置值
            const wasDisabled = drivingRegionSelect.disabled;
            drivingRegionSelect.disabled = false;
            drivingRegionSelect.value = orderData['driving-region'] || 'kl';
            // 触发change事件确保值被正确应用
            drivingRegionSelect.dispatchEvent(new Event('change', { bubbles: true }));
            // 恢复disabled状态
            drivingRegionSelect.disabled = wasDisabled;
            
            // 输出调试信息
            console.log('驾驶区域:', orderData['driving-region']);
            document.getElementById('driver').value = orderData.driver || '';
            document.getElementById('remark').value = orderData.remark || '';
        }

        // 创建订单元素
        function createOrderElement(orderData, rawText, index) {
            const currentLang = languageSelector.value;
            const orderDiv = document.createElement('div');
            orderDiv.className = 'order-item';
            
            // 添加响应式类，用于移动端适配
            const applyMobileClass = () => {
                if (window.matchMedia('(max-width: 767px)').matches) {
                    orderDiv.classList.add('order-item-mobile');
                } else {
                    orderDiv.classList.remove('order-item-mobile');
                }
            };
            
            // 初始应用
            applyMobileClass();
            
            // 使用防抖函数优化resize事件
            let resizeTimeout;
            window.addEventListener('resize', () => {
                clearTimeout(resizeTimeout);
                resizeTimeout = setTimeout(applyMobileClass, 100);
            });
            
            // 创建订单标题
            const header = document.createElement('div');
            header.style.display = 'flex';
            header.style.justifyContent = 'space-between';
            header.style.alignItems = 'center';
            header.style.marginBottom = '1rem';
            header.style.paddingBottom = '0.5rem';
            header.style.borderBottom = '1px solid #eee';
            
            const title = document.createElement('h3');
            title.style.fontSize = '1rem';
            title.style.fontWeight = '500';
            title.style.margin = '0';
            title.textContent = `${i18n[currentLang]['order-number']} #${index} - ${orderData['ota-reference'] || '未知编号'}`;
            
            const copyButton = document.createElement('button');
            copyButton.className = 'btn btn-gray';
            copyButton.style.padding = '0.25rem 0.5rem';
            copyButton.style.fontSize = '0.875rem';
            copyButton.textContent = i18n[currentLang]['copy'];
            copyButton.addEventListener('click', () => copyOrderData(orderData));
            
            header.appendChild(title);
            header.appendChild(copyButton);
            orderDiv.appendChild(header);
            
            // 创建订单详情
            const details = document.createElement('div');
            details.style.display = 'grid';
            details.style.gridTemplateColumns = 'repeat(2, 1fr)';
            details.style.gap = '0.5rem';
            details.style.fontSize = '0.875rem';
            
            // 响应式布局 - 在移动端使用单列布局
            const updateGridLayout = () => {
                if (window.matchMedia('(max-width: 767px)').matches) {
                    details.style.gridTemplateColumns = '1fr';
                } else {
                    details.style.gridTemplateColumns = 'repeat(2, 1fr)';
                }
            };
            
            // 初始应用
            updateGridLayout();
            
            // 使用防抖函数优化resize事件
            let layoutResizeTimeout;
            window.addEventListener('resize', () => {
                clearTimeout(layoutResizeTimeout);
                layoutResizeTimeout = setTimeout(updateGridLayout, 100);
            });
            
            // 添加字段
            const fieldGroups = [
                { label: i18n[currentLang]['ota-platform'], value: orderData.ota },
                { label: i18n[currentLang]['order-number'], value: orderData['ota-reference'] },
                { label: i18n[currentLang]['price'], value: orderData.price },
                { label: i18n[currentLang]['passenger-name'], value: orderData.name },
                { label: i18n[currentLang]['phone'], value: orderData.phone },
                { label: i18n[currentLang]['flight-number'], value: orderData['flight-number'] },
                { label: i18n[currentLang]['pickup-time'], value: orderData['pickup-datetime'] },
                { label: i18n[currentLang]['pickup-address'], value: orderData['pickup-address'] },
                { label: i18n[currentLang]['dropoff-address'], value: orderData['dropoff-address'] },
                { label: i18n[currentLang]['car-type'], value: orderData['vehicle-type'] || orderData['car-type'] },
                { label: i18n[currentLang]['passenger-count'], value: orderData['passenger-number'] },
                { label: i18n[currentLang]['order-type'], value: getOrderTypeLabel(orderData, currentLang) },
                { label: i18n[currentLang]['driving-region'], value: getDrivingRegionLabel(orderData['driving-region']) },
                { label: i18n[currentLang]['remarks'], value: orderData.remark }
            ];
            
            fieldGroups.forEach(field => {
                if (field.value) {
                    const fieldDiv = document.createElement('div');
                    fieldDiv.style.display = 'flex';
                    
                    const label = document.createElement('div');
                    label.style.fontWeight = '500';
                    label.style.width = '6rem';
                    label.textContent = field.label + '：';
                    
                    const value = document.createElement('div');
                    value.style.flex = '1';
                    value.textContent = field.value;
                    
                    fieldDiv.appendChild(label);
                    fieldDiv.appendChild(value);
                    details.appendChild(fieldDiv);
                }
            });
            
            orderDiv.appendChild(details);
            
            // 添加查看详细按钮
            const viewDetailsButton = document.createElement('button');
            viewDetailsButton.className = 'btn btn-primary';
            viewDetailsButton.style.marginTop = '1rem';
            viewDetailsButton.style.width = '100%';
            viewDetailsButton.textContent = i18n[currentLang]['view-details'];
            viewDetailsButton.addEventListener('click', () => viewDetailedOrder(orderData, rawText));
            
            orderDiv.appendChild(viewDetailsButton);
            
            return orderDiv;
        }

        // 获取订单类型标签（多语言支持）
        function getOrderTypeLabel(orderData, lang) {
            if (orderData.category === 'airport') {
                if (orderData.subcategory === 'pickup') {
                    return i18n[lang]['airport-pickup'];
                } else if (orderData.subcategory === 'dropoff') {
                    return i18n[lang]['airport-dropoff'];
                }
            }
            return i18n[lang]['charter'];
        }
        
        // 获取驾驶区域标签
        function getDrivingRegionLabel(regionCode) {
            const regionMap = {
                'kl': '吉隆坡',
                'penang': '槟城',
                'sg': '新加坡',
                'jb': '新山',
                'sabah': '沙巴'
            };
            
            if (!regionCode) return '';
            
            // 尝试小写匹配
            const lowerCode = regionCode.toLowerCase();
            if (regionMap[lowerCode]) {
                return regionMap[lowerCode];
            }
            
            // 如果没有匹配到，返回原始代码
            return regionCode.toUpperCase();
        }

        // 查看详细订单
        async function viewDetailedOrder(orderData, rawText) {
            // 显示标准订单表单，隐藏多订单容器
            document.querySelector('.grid').classList.remove('hidden');
            multiOrdersContainer.classList.add('hidden');
            
            // 确保地址已翻译
            if (orderData['pickup-address'] && !orderData['pickup-address-translated']) {
                orderData['pickup-address'] = await processAddress(orderData['pickup-address']);
                orderData['pickup-address-translated'] = true;
            }
            
            if (orderData['dropoff-address'] && !orderData['dropoff-address-translated']) {
                orderData['dropoff-address'] = await processAddress(orderData['dropoff-address']);
                orderData['dropoff-address-translated'] = true;
            }
            
            // 填充表单
            fillOrderForm(orderData);
            
            // 显示原始文本供参考
            rawOrderTextarea.value = rawText;
        }

        // 启用表单字段编辑
        function enableFormEditing() {
            formInputs.forEach(input => {
                input.readOnly = false;
                input.disabled = false;
            });
            
            editBtn.classList.add('hidden');
            saveBtn.classList.remove('hidden');
        }

        // 禁用表单字段编辑
        function disableFormEditing() {
            formInputs.forEach(input => {
                input.readOnly = true;
                input.disabled = true;
            });
            
            saveBtn.classList.add('hidden');
            editBtn.classList.remove('hidden');
        }

        // 复制表单数据
        function copyFormData() {
            let formattedData = '';
            
            formInputs.forEach(input => {
                if (input.id && input.value) {
                    const label = input.previousElementSibling?.textContent || input.id;
                    formattedData += `${label}: ${input.value}\n`;
                }
            });
            
            copyToClipboard(formattedData);
            showNotification(i18n[languageSelector.value]['data-copied']);
        }

        // 复制订单数据
        function copyOrderData(orderData) {
            let formattedData = '';
            
            // 字段映射
            const fieldMapping = {
                'ota': 'OTA平台',
                'ota-reference': 'OTA订单号',
                'price': '价格',
                'name': '乘客姓名',
                'phone': '电话',
                'email': '邮箱',
                'flight-number': '航班号',
                'pickup-datetime': '接机时间',
                'pickup-address': '接机地址',
                'dropoff-address': '送机地址',
                'car-type': '车型',
                'vehicle-type': '车型',
                'luggage-number': '行李数量',
                'passenger-number': '乘客人数',
                'language': '语言',
                'category': '类别',
                'subcategory': '子类别',
                'driving-region': '驾驶区域',
                'driver': '司机数量',
                'remark': '备注'
            };
            
            // 处理订单类型信息
            const currentLang = languageSelector.value;
            
            // 添加订单类型字段
            let orderTypeDisplay = '';
            
            // 根据类别和子类别确定订单类型
            if (orderData.category === 'airport') {
                if (orderData.subcategory === 'pickup') {
                    orderTypeDisplay = i18n[currentLang]['airport-pickup'] || '机场接机';
                } else if (orderData.subcategory === 'dropoff') {
                    orderTypeDisplay = i18n[currentLang]['airport-dropoff'] || '机场送机';
                } else {
                    orderTypeDisplay = '机场接送';
                }
            } else {
                orderTypeDisplay = i18n[currentLang]['charter'] || '包车服务';
            }
            
            // 添加类别和子类别字段
            if (orderData.category) {
                formattedData += `${fieldMapping['category']}: ${orderData.category}\n`;
            }
            
            if (orderData.subcategory && orderData.category === 'airport') {
                formattedData += `${fieldMapping['subcategory']}: ${orderData.subcategory}\n`;
            }
            
            // 添加订单类型字段
            formattedData += `${fieldMapping['order-type']}: ${orderTypeDisplay}\n`;
            
            // 格式化数据
            for (const [key, value] of Object.entries(orderData)) {
                // 跳过vehicle-type如果car-type已经存在且相同
                if (key === 'vehicle-type' && orderData['car-type'] === value) {
                    continue;
                }
                
                // 跳过已经处理的订单类型相关字段
                if (key === 'order-type' || key === 'category' || key === 'subcategory') {
                    continue;
                }
                
                if (value && fieldMapping[key]) {
                    let displayValue = value;
                    
                    if (key === 'driving-region') {
                        displayValue = getDrivingRegionLabel(value);
                    }
                    
                    formattedData += `${fieldMapping[key]}: ${displayValue}\n`;
                }
            }
            
            copyToClipboard(formattedData);
            showNotification(i18n[languageSelector.value]['data-copied']);
        }

        // 复制内容到剪贴板
        function copyToClipboard(text) {
            const textarea = document.createElement('textarea');
            textarea.value = text;
            document.body.appendChild(textarea);
            textarea.select();
            document.execCommand('copy');
            document.body.removeChild(textarea);
        }

        // 显示通知
        function showNotification(message) {
            notification.textContent = message;
            notification.classList.add('show');
            
            setTimeout(() => {
                notification.classList.remove('show');
            }, 3000);
        }

        // 事件监听器
        convertBtn.addEventListener('click', convertOrder);
        
        resetBtn.addEventListener('click', function() {
            rawOrderTextarea.value = '';
        });
        
        editBtn.addEventListener('click', function() {
            enableFormEditing();
            showNotification(i18n[languageSelector.value]['edit-enabled']);
        });
        
        saveBtn.addEventListener('click', function() {
            disableFormEditing();
            showNotification(i18n[languageSelector.value]['changes-saved']);
        });
        
        copyOutputBtn.addEventListener('click', copyFormData);

        // 语言选择器事件
        languageSelector.addEventListener('change', function() {
            const selectedLang = this.value;
            updateUILanguage(selectedLang);
        });

        // 页面加载时检查是否有来自sessionStorage的订单数据并处理
        document.addEventListener('DOMContentLoaded', function() {
            // 设置初始语言
            const savedLang = localStorage.getItem('preferred-language') || 'zh';
            languageSelector.value = savedLang;
            updateUILanguage(savedLang);
            
            // 应用设备特定样式
            applyDeviceSpecificStyles();
            
            const orderData = sessionStorage.getItem('orderData');
            if (orderData) {
                rawOrderTextarea.value = orderData;
                // 清除sessionStorage中的数据
                sessionStorage.removeItem('orderData');
                // 自动处理订单
                convertBtn.click();
            }
            
            // 监听窗口大小变化
            let resizeTimeout;
            window.addEventListener('resize', function() {
                clearTimeout(resizeTimeout);
                resizeTimeout = setTimeout(applyDeviceSpecificStyles, 100);
            });
        });

        // 设备特定样式应用函数
        function applyDeviceSpecificStyles() {
            const isMobile = window.matchMedia('(max-width: 767px)').matches;
            
            // 应用移动端特定样式
            if (isMobile) {
                // 调整按钮大小和触摸目标
                document.querySelectorAll('.btn').forEach(btn => {
                    btn.style.minHeight = '44px';
                });
                
                // 调整表单元素大小
                document.querySelectorAll('.form-input, textarea, select').forEach(input => {
                    input.style.fontSize = '16px';
                    input.style.padding = '0.625rem';
                });
                
                // 调整多订单容器
                const ordersList = document.getElementById('orders-list');
                if (ordersList) {
                    ordersList.style.gridTemplateColumns = '1fr';
                    ordersList.style.gap = '0.75rem';
                    ordersList.style.padding = '0.75rem';
                }
                
                // 添加移动端类到订单项
                document.querySelectorAll('.order-item').forEach(item => {
                    item.classList.add('order-item-mobile');
                });
            } else {
                // 恢复桌面端样式
                document.querySelectorAll('.btn').forEach(btn => {
                    btn.style.minHeight = '';
                });
                
                document.querySelectorAll('.form-input, textarea, select').forEach(input => {
                    input.style.fontSize = '';
                    input.style.padding = '';
                });
                
                const ordersList = document.getElementById('orders-list');
                if (ordersList) {
                    ordersList.style.gridTemplateColumns = 'repeat(auto-fill, minmax(280px, 1fr))';
                    ordersList.style.gap = '1rem';
                    ordersList.style.padding = '1rem';
                }
                
                // 移除移动端类
                document.querySelectorAll('.order-item').forEach(item => {
                    item.classList.remove('order-item-mobile');
                });
            }
        }
        
        // API密钥配置 - 使用统一配置文件
        // 注意：API密钥现在通过api-config.js统一管理
        
        // 使用Gemini API查询POI英文名称
        async function queryGeminiPOIEnglishName(chineseName) {
            if (!chineseName || chineseName.trim().length === 0) return chineseName;
            
            try {
                const response = await fetch(
                    `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${window.getGeminiApiKey()}`,
                    {
                        method: "POST",
                        headers: {
                            "Content-Type": "application/json"
                        },
                        body: JSON.stringify({
                            contents: [{
                                parts: [{
                                    text: `你是一个专业的酒店名称翻译专家。请将中文酒店名称翻译为官方标准英文名称。

翻译规则：
1. 返回格式：仅返回英文名称，不含引号、括号或解释文字
2. 大小写：每个单词首字母大写（Title Case），介词除外
3. 品牌处理：保持国际连锁酒店的官方英文品牌名
4. 地名处理：使用标准英文地名拼写
5. 类型词汇：Hotel, Resort, Suites, Inn, Lodge 等保持英文

示例：
- 新加坡史各士皇族酒店 → Royal Plaza on Scotts
- 吉隆坡希尔顿酒店 → Hilton Kuala Lumpur
- 槟城东方大酒店 → Eastern & Oriental Hotel

请翻译：${chineseName}

只返回英文名称：`
                                }]
                            }],
                            generationConfig: {
                                temperature: 0.2,
                                maxOutputTokens: 100,
                                topP: 0.95,
                                topK: 40
                            }
                        })
                    }
                );
                
                const data = await response.json();
                console.log("Gemini API返回结果:", data);
                
                // 正确处理 Gemini API 响应格式
                if (data && data.candidates && data.candidates.length > 0 && 
                    data.candidates[0].content && data.candidates[0].content.parts && 
                    data.candidates[0].content.parts.length > 0 && data.candidates[0].content.parts[0].text) {
                    
                    // 输出完整的响应结构以便调试
                    console.log('原始 Gemini 响应数据:', JSON.stringify(data.candidates[0].content, null, 2));
                    
                    // 提取翻译结果并清理
                    let result = data.candidates[0].content.parts[0].text.trim();
                    console.log('原始翻译结果:', result);
                    
                    // 处理可能的引号和额外文本
                    result = result.replace(/^["']|["']$/g, ''); // 去除引号
                    
                    // 处理可能的解释性文本
                    if (result.includes('\n')) {
                        console.log('检测到多行结果，只保留第一行');
                        result = result.split('\n')[0]; // 只取第一行
                    }
                    
                    // 处理可能的前缀文本
                    const prefixes = ['英文名称：', '英文名称:', 'English name:', 'English Name:', 'Translation:'];
                    for (const prefix of prefixes) {
                        if (result.startsWith(prefix)) {
                            console.log(`检测到前缀 "${prefix}"，已移除`);
                            result = result.substring(prefix.length).trim();
                            break;
                        }
                    }
                    
                    // 验证结果是否符合预期
                    // 1. 检查是否包含中文
                    if (/[\u4e00-\u9fa5]/.test(result)) {
                        console.warn('翻译结果仍包含中文字符:', result);
                        throw new Error('翻译结果包含中文字符');
                    }
                    
                    // 2. 检查是否为空
                    if (!result || result.trim() === '') {
                        console.warn('翻译结果为空');
                        throw new Error('翻译结果为空');
                    }
                    
                    // 3. 检查是否符合酒店名称格式（首字母大写）
                    if (!/^[A-Z]/.test(result)) {
                        console.warn('翻译结果首字母不是大写:', result);
                        // 自动修正首字母大写
                        result = result.charAt(0).toUpperCase() + result.slice(1);
                    }
                    
                    // 4. 检查是否包含特殊字符
                    if (/[\*\(\)\[\]\{\}\<\>\|\\\^]/.test(result)) {
                        console.warn('翻译结果包含特殊字符:', result);
                        // 移除特殊字符
                        result = result.replace(/[\*\(\)\[\]\{\}\<\>\|\\\^]/g, '');
                    }
                    
                    console.log('处理后的翻译结果:', result);
                    return result;
                } else {
                    console.error('无法从 Gemini API 响应中提取翻译结果:', data);
                    throw new Error('无法从 Gemini API 响应中提取翻译结果');
                }
            } catch (error) {
                console.error("Gemini API请求失败:", error);
                throw error; // 抛出错误以便Promise.race处理
            }
        }
        
        // 使用DeepSeek API查询POI英文名称
        async function queryDeepSeekPOIEnglishName(chineseName) {
            if (!chineseName || chineseName.trim().length === 0) return chineseName;
            
            try {
                const response = await fetch(
                    "https://api.deepseek.com/chat/completions",
                    {
                        method: "POST",
                        headers: {
                            "Authorization": `Bearer ${window.getDeepSeekApiKey()}`,
                            "Content-Type": "application/json"
                        },
                        body: JSON.stringify({
                            model: "deepseek-chat",
                            messages: [
                                {
                                    role: "system",
                                    content: "You are a professional hotel name translation expert. Translate Chinese hotel names to their official standard English names. Rules: 1) Return ONLY the English name, no quotes or explanations 2) Use Title Case for all words except prepositions 3) Preserve official brand names for international hotel chains 4) Use standard English spelling for place names 5) Keep hotel type words in English (Hotel, Resort, Suites, Inn, Lodge, etc.)"
                                },
                                {
                                    role: "user",
                                    content: `Translate this Chinese hotel name to official English name: ${chineseName}

Examples:
- 新加坡史各士皇族酒店 → Royal Plaza on Scotts
- 吉隆坡希尔顿酒店 → Hilton Kuala Lumpur  
- 槟城东方大酒店 → Eastern & Oriental Hotel

English name only:`
                                }
                            ],
                            temperature: 0.2, // 降低随机性，提高翻译准确性
                            max_tokens: 100, // 限制输出长度
                            stream: false
                        })
                    }
                );
                
                const data = await response.json();
                console.log("DeepSeek API返回结果:", data);
                
                // 正确处理 DeepSeek API 响应格式
                if (data && data.choices && data.choices.length > 0 && data.choices[0].message && data.choices[0].message.content) {
                    // 输出完整的响应结构以便调试
                    console.log('原始 DeepSeek 响应数据:', JSON.stringify(data.choices[0].message, null, 2));
                    
                    // 提取翻译结果并清理
                    let result = data.choices[0].message.content.trim();
                    console.log('原始翻译结果:', result);
                    
                    // 处理可能的引号和额外文本
                    result = result.replace(/^["']|["']$/g, ''); // 去除引号
                    
                    // 处理可能的解释性文本
                    if (result.includes('\n')) {
                        console.log('检测到多行结果，只保留第一行');
                        result = result.split('\n')[0]; // 只取第一行
                    }
                    
                    // 处理可能的前缀文本
                    const prefixes = ['英文名称：', '英文名称:', 'English name:', 'English Name:', 'Translation:'];
                    for (const prefix of prefixes) {
                        if (result.startsWith(prefix)) {
                            console.log(`检测到前缀 "${prefix}"，已移除`);
                            result = result.substring(prefix.length).trim();
                            break;
                        }
                    }
                    
                    // 验证结果是否符合预期
                    // 1. 检查是否包含中文
                    if (/[\u4e00-\u9fa5]/.test(result)) {
                        console.warn('翻译结果仍包含中文字符:', result);
                        throw new Error('翻译结果包含中文字符');
                    }
                    
                    // 2. 检查是否为空
                    if (!result || result.trim() === '') {
                        console.warn('翻译结果为空');
                        throw new Error('翻译结果为空');
                    }
                    
                    // 3. 检查是否符合酒店名称格式（首字母大写）
                    if (!/^[A-Z]/.test(result)) {
                        console.warn('翻译结果首字母不是大写:', result);
                        // 自动修正首字母大写
                        result = result.charAt(0).toUpperCase() + result.slice(1);
                    }
                    
                    // 4. 检查是否包含特殊字符
                    if (/[\*\(\)\[\]\{\}\<\>\|\\\^]/.test(result)) {
                        console.warn('翻译结果包含特殊字符:', result);
                        // 移除特殊字符
                        result = result.replace(/[\*\(\)\[\]\{\}\<\>\|\\\^]/g, '');
                    }
                    
                    console.log('处理后的翻译结果:', result);
                    return result;
                } else {
                    console.error('无法从 DeepSeek API 响应中提取翻译结果:', data);
                    throw new Error('无法从 DeepSeek API 响应中提取翻译结果');
                }
            } catch (error) {
                console.error("DeepSeek API请求失败:", error);
                throw error; // 抛出错误以便Promise.race处理
            }
        }
        
        // 评估翻译结果的质量
        function evaluateTranslationQuality(chineseName, englishName, source) {
            console.group(`翻译质量评估 (${source})`);
            
            // 1. 基本信息
            console.log('%c原始中文名称: %c' + chineseName, 'font-weight: bold', 'font-weight: normal');
            console.log('%c翻译英文名称: %c' + englishName, 'font-weight: bold', 'font-weight: normal');
            
            // 2. 长度评估
            const chineseLength = chineseName.length;
            const englishLength = englishName.length;
            const lengthRatio = englishLength / chineseLength;
            let lengthStatus = '正常';
            let lengthColor = 'color: green';
            
            if (lengthRatio < 0.8) {
                lengthStatus = '过短 (可能翻译不完整)';
                lengthColor = 'color: orange';
            } else if (lengthRatio > 3) {
                lengthStatus = '过长 (可能包含多余内容)';
                lengthColor = 'color: orange';
            }
            
            console.log(`%c长度比例: %c${chineseLength} -> ${englishLength} (${lengthRatio.toFixed(2)}) %c${lengthStatus}`, 
                'font-weight: bold', 'font-weight: normal', lengthColor);
            
            // 3. 格式评估
            const hasProperCapitalization = /^[A-Z]/.test(englishName) && 
                                          englishName.split(' ').every(word => 
                                            /^[A-Z]/.test(word) || 
                                            ['a', 'an', 'the', 'in', 'on', 'at', 'by', 'for', 'with', 'of'].includes(word.toLowerCase()));
            
            console.log(`%c大小写格式: %c${hasProperCapitalization ? '正确' : '不正确'}`, 
                'font-weight: bold', hasProperCapitalization ? 'color: green' : 'color: red');
            
            // 4. 特殊字符评估
            const hasSpecialChars = /[\*\(\)\[\]\{\}\<\>\|\\\^]/.test(englishName);
            console.log(`%c特殊字符: %c${hasSpecialChars ? '存在' : '无'}`, 
                'font-weight: bold', hasSpecialChars ? 'color: red' : 'color: green');
            
            // 5. 总体评估
            let overallScore = 5; // 满分为5分
            if (!hasProperCapitalization) overallScore -= 1;
            if (hasSpecialChars) overallScore -= 1;
            if (lengthRatio < 0.8 || lengthRatio > 3) overallScore -= 1;
            
            let qualityLevel = '优秀';
            let qualityColor = 'color: green';
            
            if (overallScore <= 2) {
                qualityLevel = '差';
                qualityColor = 'color: red';
            } else if (overallScore <= 3) {
                qualityLevel = '一般';
                qualityColor = 'color: orange';
            } else if (overallScore <= 4) {
                qualityLevel = '良好';
                qualityColor = 'color: blue';
            }
            
            console.log(`%c总体质量: %c${overallScore}/5 (${qualityLevel})`, 'font-weight: bold', qualityColor);
            console.groupEnd();
            
            return {
                score: overallScore,
                quality: qualityLevel,
                details: {
                    lengthRatio,
                    hasProperCapitalization,
                    hasSpecialChars
                }
            };
        }
        
        // 同时调用两个API，使用先返回的结果
        async function queryPOIEnglishName(chineseName) {
            if (!chineseName || chineseName.trim().length === 0) return chineseName;
            
            // 添加超时处理
            const timeout = (ms) => new Promise((_, reject) => 
                setTimeout(() => reject(new Error('API请求超时')), ms)
            );
            
            try {
                // 创建两个 Promise 来跟踪两个 API 的结果
                const geminiPromise = queryGeminiPOIEnglishName(chineseName)
                    .then(result => ({ source: 'Gemini', result }));
                
                const deepseekPromise = queryDeepSeekPOIEnglishName(chineseName)
                    .then(result => ({ source: 'DeepSeek', result }));
                
                // 同时发起两个API请求，使用先返回的结果
                const result = await Promise.race([
                    geminiPromise,
                    deepseekPromise,
                    timeout(10000) // 10秒超时
                ]);
                
                console.log(`使用 ${result.source} API的结果: ${result.result}`);
                
                // 评估翻译质量
                evaluateTranslationQuality(chineseName, result.result, result.source);
                
                // 尝试获取另一个 API 的结果进行比较
                try {
                    const otherPromise = result.source === 'Gemini' ? deepseekPromise : geminiPromise;
                    const otherResult = await Promise.race([otherPromise, timeout(3000)]);
                    
                    if (otherResult && otherResult.result) {
                        console.log(`另一个 API (${otherResult.source}) 的结果: ${otherResult.result}`);
                        evaluateTranslationQuality(chineseName, otherResult.result, otherResult.source);
                        
                        // 比较两个结果
                        if (result.result !== otherResult.result) {
                            console.log('%c两个 API 结果不同，进行质量比较', 'color: purple; font-weight: bold');
                            const firstQuality = evaluateTranslationQuality(chineseName, result.result, result.source);
                            const secondQuality = evaluateTranslationQuality(chineseName, otherResult.result, otherResult.source);
                            
                            // 如果第二个结果质量明显更好，使用第二个结果
                            if (secondQuality.score > firstQuality.score + 1) {
                                console.log(`%c更改为使用质量更好的 ${otherResult.source} 结果`, 'color: blue; font-weight: bold');
                                return otherResult.result;
                            }
                        }
                    }
                } catch (error) {
                    console.log('获取另一个 API 结果失败，仅使用先返回的结果');
                }
                
                return result.result;
            } catch (error) {
                console.error("所有API请求均失败:", error);
                return chineseName; // 返回原始中文名称作为后备
            }
        }
        
        // 酒店名称映射表（本地缓存）
        let hotelNameMapping = {};
        
        // 尝试从localStorage加载缓存的翻译结果
        try {
            const savedMapping = localStorage.getItem('hotelNameMapping');
            if (savedMapping) {
                hotelNameMapping = JSON.parse(savedMapping);
                console.log('已从本地存储加载酒店名称映射缓存');
            }
        } catch (error) {
            console.error('加载本地缓存失败:', error);
            // 初始化默认映射
            hotelNameMapping = {
                '新加坡史各士皇族酒店': 'Royal Plaza on Scotts',
                '新加坡良木园酒店': 'Goodwood Park Hotel'
                // 其他现有映射...
            };
        }
        
        // 保存酒店名称映射到localStorage
        function saveHotelNameMapping() {
            try {
                localStorage.setItem('hotelNameMapping', JSON.stringify(hotelNameMapping));
                console.log('酒店名称映射已保存到本地存储');
            } catch (error) {
                console.error('保存酒店名称映射失败:', error);
            }
        }
        
        // 获取酒店英文名称（带重试机制）
        async function getHotelEnglishName(chineseName) {
            // 参数验证
            if (!chineseName || typeof chineseName !== 'string' || chineseName.trim() === '') {
                console.warn('无效的酒店中文名称:', chineseName);
                return chineseName || '';
            }
            
            // 检查本地缓存
            if (hotelNameMapping[chineseName]) {
                console.log(`使用缓存的翻译: ${chineseName} -> ${hotelNameMapping[chineseName]}`);
                return hotelNameMapping[chineseName];
            }
            
            // 本地模糊匹配
            const fuzzyMatch = fuzzyMatchLocal(chineseName);
            if (fuzzyMatch) {
                console.log(`使用模糊匹配的翻译: ${chineseName} -> ${fuzzyMatch}`);
                hotelNameMapping[chineseName] = fuzzyMatch;
                saveHotelNameMapping();
                return fuzzyMatch;
            }
            
            // 尝试API翻译，最多重试2次
            let retries = 0;
            const maxRetries = 2;
            
            while (retries <= maxRetries) {
                try {
                    console.log(`尝试API翻译 (尝试 ${retries + 1}/${maxRetries + 1}): ${chineseName}`);
                    const englishName = await queryPOIEnglishName(chineseName);
                    
                    // 验证翻译结果
                    if (englishName && englishName !== chineseName) {
                        console.log(`API翻译成功: ${chineseName} -> ${englishName}`);
                        hotelNameMapping[chineseName] = englishName;
                        saveHotelNameMapping();
                        return englishName;
                    } else {
                        throw new Error('翻译结果无效或未变化');
                    }
                } catch (error) {
                    console.error(`API翻译失败 (尝试 ${retries + 1}/${maxRetries + 1}):`, error);
                    retries++;
                    
                    if (retries > maxRetries) {
                        console.warn(`已达到最大重试次数，返回原始中文名称: ${chineseName}`);
                        return chineseName;
                    }
                    
                    // 重试前等待一段时间
                    await new Promise(resolve => setTimeout(resolve, 1000 * retries));
                }
            }
            
            return chineseName; // 所有尝试都失败，返回原始中文名称
        }
    // 防抖函数：优化窗口大小变化事件处理
    function debounce(func, wait) {
        let timeout;
        return function() {
            const context = this;
            const args = arguments;
            clearTimeout(timeout);
            timeout = setTimeout(() => func.apply(context, args), wait);
        };
    }
    
    // 设备检测函数：检测当前设备类型
    function detectDevice() {
        const userAgent = navigator.userAgent.toLowerCase();
        const isMobile = /iphone|ipod|android|blackberry|windows phone/g.test(userAgent);
        const isTablet = /(ipad|tablet|playbook|silk)|(android(?!.*mobile))/g.test(userAgent);
        const isIOS = /iphone|ipod|ipad/g.test(userAgent);
        const isAndroid = /android/g.test(userAgent);
        const isEdge = /edg/g.test(userAgent);
        
        return {
            isMobile,
            isTablet,
            isDesktop: !isMobile && !isTablet,
            isIOS,
            isAndroid,
            isEdge
        };
    }
    
    // 应用设备特定样式
    function applyDeviceSpecificStyles() {
        const device = detectDevice();
        const html = document.documentElement;
        
        // 清除现有设备类
        html.classList.remove('mobile', 'tablet', 'desktop', 'ios', 'android', 'edge');
        
        // 添加设备类
        if (device.isMobile) html.classList.add('mobile');
        if (device.isTablet) html.classList.add('tablet');
        if (device.isDesktop) html.classList.add('desktop');
        if (device.isIOS) html.classList.add('ios');
        if (device.isAndroid) html.classList.add('android');
        if (device.isEdge) html.classList.add('edge');
        
        // 性能优化
        if (device.isMobile || device.isTablet) {
            // 移动端优化图片加载
            document.querySelectorAll('img').forEach(img => {
                if (!img.loading) img.loading = 'lazy';
            });
            
            // 减少移动端动画复杂度
            document.querySelectorAll('.card, .order-item, .order-card').forEach(el => {
                el.style.transition = 'transform 0.2s ease-in-out';
            });
        }
        
        // Edge浏览器特定优化
        if (device.isEdge) {
            // 使用兼容性更好的渲染方式
            document.body.style.textRendering = 'auto';
        }
        
        // 检测是否支持背景模糊
        const supportsBackdropFilter = CSS.supports('backdrop-filter', 'blur(10px)') || 
                                    CSS.supports('-webkit-backdrop-filter', 'blur(10px)');
        
        // 如果不支持背景模糊，使用替代方案
        if (!supportsBackdropFilter) {
            const loader = document.getElementById('api-loader');
            if (loader) {
                loader.style.backdropFilter = 'none';
                loader.style.webkitBackdropFilter = 'none';
                loader.style.backgroundColor = 'rgba(0, 0, 0, 0.8)';
            }
        }
    }
    
    // 页面加载完成后初始化
    window.addEventListener('load', function() {
        // 应用设备特定样式
        applyDeviceSpecificStyles();
        
        // 监听窗口大小变化，使用防抖函数优化性能
        window.addEventListener('resize', debounce(function() {
            applyDeviceSpecificStyles();
        }, 250));
        
        // 性能优化：延迟加载非关键资源
        setTimeout(() => {
            // 预加载常用页面
            const links = document.querySelectorAll('a[href]');
            links.forEach(link => {
                if (link.hostname === window.location.hostname) {
                    const prefetchLink = document.createElement('link');
                    prefetchLink.rel = 'prefetch';
                    prefetchLink.href = link.href;
                    document.head.appendChild(prefetchLink);
                }
            });
        }, 2000);
    });
    </script>
    <!-- API加载器 -->
    <div id="api-loader">
        <div class="spinner-container">
            <div class="spinner"></div>
            <div class="spinner-text">正在处理，请稍候...</div>
        </div>
    </div>
</body>
</html>