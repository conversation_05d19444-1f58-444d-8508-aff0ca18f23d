# 订单处理系统

这是一个使用Google Gemini 2.0 Flash API的订单文本处理系统。该系统可以解析非结构化文本，并从中提取订单信息。

## 功能特点

1. 使用Google Gemini 2.0 Flash API进行自然语言理解
2. 支持多订单处理和识别
3. 自动校验和补全订单信息
4. 支持不同类型订单的智能解析（接机/送机/日租等）
5. 自动标准化车型信息
6. 智能处理地址信息，确保接/送机订单地址正确

## 消息处理器列表

系统集成了以下消息处理器：

| 处理器名称 | 功能描述 |
|------------|----------|
| OrderTextProcessor | 解析订单文本，提取订单信息 |
| OrderValidator | 验证订单数据完整性和正确性 |
| DefaultValueProcessor | 根据订单类型自动补全默认值 |
| AddressProcessor | 处理和验证地址信息，特别是机场相关订单 |
| VehicleTypeNormalizer | 标准化车型信息（五座/七座） |
| MultiOrderManager | 管理多订单的展示和操作 |

## API集成

### Google Gemini 2.0 Flash API

系统使用Google Gemini 2.0 Flash API进行自然语言处理。该API具有以下特点：

- 模型: `gemini-2.0-flash`
- 性能: 快速响应，适合实时应用
- 支持: 多语言支持，包括中文、英文等
- 文档: [Google Gemini API文档](https://ai.google.dev/gemini-api/docs/models#gemini-2.0-flash)

## 依赖项

| 依赖名称 | 版本 | 用途 |
|----------|------|------|
| Google Gemini API | 2.0 | 自然语言处理 |
| jQuery | 最新版 | DOM操作和事件处理 |
| Bootstrap | 最新版 | UI组件和样式 |

## 使用方法

1. 在文本区域输入订单文本
2. 点击"处理订单"按钮
3. 系统会自动解析文本并提取订单信息
4. 验证订单信息后，系统会显示提取的订单详情
5. 可以编辑、保存或导出订单数据

## 最近更新

- 统一了单订单和多订单处理逻辑，确保一致的数据流和相同的验证标准
- 修复了接机订单和送机订单的地址处理逻辑
- 确保接机订单的pickup-address为机场，dropoff-address为酒店
- 确保送机订单的pickup-address为酒店，dropoff-address为机场
- 增强了车型标准化功能，支持更多同义词识别
- 优化航班时间格式识别功能，支持多种格式：HHMM-HHMM、HH:MM-HH:MM、HH:MM--HH:MM，以及+1跨天标识

## 字段说明

- **OTA**: 订单来源平台（如：Fliggy）
- **OTA reference**: 订单编号（19位数字）
- **price**: 总价格
- **Name**: 乘客姓名
- **Phone**: 联系电话
- **Email**: 电子邮箱
- **flight number**: 航班号（字母和数字组合）
- **Pickup date & time**: 接机时间（格式：DD-MM-YYYY HH:MM）
- **Pickup address**: 接机地址
- **Dropoff address**: 送机地址
- **car type**: 车型
- **passenger number**: 乘客人数
- **luggage number**: 行李数量
- **language**: 语言（默认：Chinese）
- **Category**: 订单类型（airport, charter）  
  - 接机/送机订单自动设置为airport
  - 其他订单设置为charter
- **Subcategory**: 子类型（pickup, dropoff）
- **Driving region**: 服务区域（kl, penang, sg, jb, Sabah）
- **Driver**: 司机数量（默认为1）
- **Remark**: 其他备注信息 

## 系统架构

系统采用模块化设计，每个OTA平台都有独立的处理页面：

- **index.html**: 主页面，负责OTA自动识别并跳转到对应处理页面
- **fliggy.html**: 专门处理飞猪(Fliggy)订单
- **kkday.html**: 专门处理Kkday订单

所有HTML页面都内嵌了需要的CSS和JavaScript代码，无需额外的依赖文件。

### 功能特性

- **多订单批量处理**: 系统可以自动识别并批量处理多个订单，提供统一的界面查看和管理批量订单
- **多语言界面支持**: 支持中文、英文、日文和韩文四种语言界面，用户可随时切换
- **订单自动识别**: 根据订单文本特征自动识别OTA来源和订单类型
- **地址POI翻译**: 自动识别并翻译地址中的中文POI信息
- **机场本地翻译**: 通过内置的机场映射表快速准确地将中文机场名称转换为标准英文名称，支持60+机场名称及其变体，无需依赖外部API
- **表单编辑和验证**: 支持编辑模式，可以修改和完善提取出的订单信息
- **会话存储**: 使用sessionStorage实现页面间数据传递，提升用户体验

## 飞猪(Fliggy)订单字段识别规则

飞猪订单数据可以通过以下特征识别：
- 订单编号：19位数字
- 买家信息
- 支付时间
- 查看详情标签
- 车型信息（如：舒适7座、经济5座）
- 接送类型（【接机】或【送机】）
- 地点信息（出发/抵达）
- 航班信息和预计抵达时间

多订单识别使用"订单编号："作为分割标记。 

## Kkday 订单字段识别规则

Kkday 订单数据可以通过以下特征识别：
- 订单编号：字母和数字组合
- "私人机场接送" 关键词
- "成本金额" 字段
- 接駁日期和时间
- 航班到达日期及时间
- 上车地点或航厦
- 下车地点
- 航班信息
- 乘客和行李数量

字段映射规则：
- **订单编号**: 匹配 "订单编号 : " 后的内容
- **乘客姓名**: 匹配 "订购人：" 后的内容
- **电话**: 匹配 "订购人电话：" 后的内容
- **邮箱**: 匹配 "订购人E-mail：" 后的内容
- **航班号**: 匹配 "航班编号" 后的内容
- **接机时间**: 组合 "接駁日期" 和 "接駁時間" 或 "航班到达日期及时间"
- **接机地址**: 匹配 "上车地点" 或 "航厦" 后的内容
- **送机地址**: 匹配 "吉隆坡市区 至 " 或 "下车地点" 后的内容
- **车型**: 匹配 "车辆：" 后的内容
- **乘客人数**: 匹配 "成人人数" 后的内容
- **行李数量**: 匹配 "手提行李数量" 或 "托运行李数量" 后的内容
- **价格**: 匹配 "成本金额MYR " 后的内容
- **备注**: 匹配 "备注：" 后的内容

默认语言设置为 English。

## 多语言支持

系统界面支持以下四种语言：

1. **中文**: 默认语言，适合中国用户
2. **英文**: 国际通用语言
3. **日文**: 支持日本客户
4. **韩文**: 支持韩国客户

系统所有页面（index.html、fliggy.html和kkday.html）都已统一添加多语言支持。语言选择会被保存在localStorage中，下次访问时自动使用上次选择的语言，并在不同页面间共享语言设置。语言切换不影响订单处理功能，仅更改用户界面文本。

各语言版本包含以下本地化内容：
- 页面标题和表单标签
- 按钮文本和提示信息
- 订单类型和字段值描述
- 通知和错误信息

## 多语言接送机识别

系统能够根据以下多种语言的关键词自动识别订单的接送机类型：

### 接机关键词：
- 中文：接机、接送机、接车、机场接、接送服务
- 英文：airport pickup、pick up、pick-up、arrival、meet and greet
- 日文：お迎え、空港送迎、出迎え
- 韩文：공항 픽업、픽업、마중

### 送机关键词：
- 中文：送机、去机场、机场送、送往机场
- 英文：airport drop-off、drop off、departure、send to airport
- 日文：空港へ、見送り、送り
- 韩文：공항 드랍、드랍、배웅

如果同时检测到接机和送机关键词，系统会进一步分析接送地点是否包含机场字段来判断服务类型。

## 机场本地翻译系统

为了提高地址处理的准确性和效率，系统集成了全面的机场本地翻译功能，无需依赖外部翻译API。此功能特别针对东南亚地区的机场地址，主要分为以下几个部分：

### 翻译映射表

系统内置了完整的机场名称映射表（`orderRecognitionRules.airportTranslations`），涵盖：

1. **吉隆坡及周边机场**：
   - 吉隆坡国际机场（KLIA）及其各航站楼
   - 多种常见表达方式（如：KLIA1、KLIA2、吉隆坡T1等）

2. **新加坡机场**：
   - 樟宜国际机场及其四个航站楼
   - 各种中文表达形式

3. **沙巴及周边机场**：
   - 亚庇国际机场（哥打京那巴鲁）
   - 斗湖机场、仙本那机场、山打根机场等

4. **其他马来西亚机场**：
   - 槟城国际机场、新山机场
   - 兰卡威、古晋、马六甲等区域机场
   - **梳邦机场** (Sultan Abdul Aziz Shah Airport)：支持多种表达方式（梳邦、苏邦、雪邦等）

### 智能翻译逻辑

翻译过程使用多层次匹配策略：

1. **直接映射匹配**：优先检查是否已是英文机场名称，然后尝试在映射表中查找完全匹配
2. **模糊匹配** (v2.3新增)：
   - 使用Levenshtein距离算法计算相似度
   - 自动分割地址并识别可能的机场部分
   - 对机场部分进行相似度匹配（阈值70%）
   - 智能替换最佳匹配部分
3. **城市名提取**：如果没有匹配，系统会尝试提取城市名称并构建标准机场名称
4. **关键词识别**：通过机场关键词（"机场"、"航站"等）辅助识别和翻译
5. **特殊机场处理**：为特定机场（如梳邦机场）提供专门处理逻辑

### 集成位置

此功能主要在ctrip.html中实现，通过`translateAirportAddress`函数处理接送机订单的机场地址翻译。系统会自动检测接机订单的pickup地址和送机订单的dropoff地址，确保这些地址正确地翻译为标准英文机场名称。

### 优势

- **无需网络请求**：所有翻译在本地完成，无延迟
- **专业准确**：针对旅游和交通场景优化，确保使用标准官方名称
- **多变体支持**：支持同一机场的多种表达方式和俗称
- **模糊匹配**：即使输入有拼写错误或格式不同，仍能准确识别
- **自动回退**：当无法精确匹配时，会尝试通过城市名构建合理的机场名称

此功能显著提高了订单处理中机场地址的规范化质量，确保下游系统能正确理解和处理目的地信息。

## 统一识别规则数据结构

为了提高代码可维护性和扩展性，系统引入了统一识别规则数据块 `orderRecognitionRules`，集中管理所有与订单识别相关的规则和映射。这种结构使添加新的OTA平台或修改现有规则变得更加简单。

统一数据块包含以下组件：

1. **otaRules**: OTA平台规则
   - fliggy: 飞猪订单识别规则
   - 可扩展添加其他OTA平台规则

2. **vehicleStandard**: 车型标准映射
   - 将中文车型描述映射到标准英文名称

3. **flightNumberPatterns**: 航班号识别模式
   - 支持多种航班号格式的正则表达式

4. **seatPatterns**: 座位类型识别
   - 识别各种座位数量表述方式

5. **regionMapping**: 区域识别
   - 根据关键词识别驾驶区域

6. **orderTypeKeywords**: 订单类型关键词
   - pickup: 接机关键词
   - dropoff: 送机关键词
   - airport: 机场相关关键词

7. **freeFormatPatterns**: 自由格式订单识别模式
   - 用于识别非标准格式的订单信息

```javascript
// 统一识别规则数据块示例
const orderRecognitionRules = {
    otaRules: {
        "chong dealer": {
            id: 1,
            provider: 'chong dealer',
            fieldMappings: [...]
        },
        // 可扩展添加其他OTA平台规则
    },
    vehicleStandard: {...},
    flightNumberPatterns: [...],
    seatPatterns: [...],
    regionMapping: [...],
    orderTypeKeywords: {...},
    freeFormatPatterns: {...}
};
```

## 通信消息处理器列表

| 消息类型 | 处理逻辑位置 | 版本 |
|----------|--------------|------|
| OTA识别和跳转 | index.html | 1.0 |
| Fliggy订单处理 | fliggy.html | 1.0 |
| Kkday订单处理 | kkday.html | 1.0 |
| Chong订单处理 | chong.html | 2.1 |
| 携程专车订单处理 | ctrip.html | 2.2 |
| 地址POI翻译 | fliggy.html, kkday.html, chong.html, ctrip.html (processAddress函数) | 1.0 |
| 多订单批量处理 | fliggy.html, kkday.html, chong.html, ctrip.html (processMultipleOrders函数) | 2.0 |
| 多语言界面 | index.html, fliggy.html, kkday.html, chong.html, ctrip.html (i18n对象和updateUILanguage函数) | 2.0 |
| 统一识别规则 | chong.html, ctrip.html (orderRecognitionRules对象) | 2.1 |
| 自由格式订单识别 | chong.html, ctrip.html (OrderPatternEnhancer模块) | 2.1 |
| 机场地址直接翻译 | fliggy.html, ctrip.html (processAddress函数) | 2.1 |
| 高级机场关键词匹配 | fliggy.html, ctrip.html (processAddress函数中的机场关键词匹配部分) | 2.2 |
| 机场本地翻译 | ctrip.html (orderRecognitionRules.airportTranslations和translateAirportAddress函数) | 2.4 |

## 驾驶区域标准代码

系统使用以下标准代码表示驾驶区域：

```javascript
const regionMapping = [
    { code: 'kl', keywords: ['kuala lumpur', 'kl', 'selangor', '吉隆坡', '雪兰莪'] },
    { code: 'penang', keywords: ['penang', '槟城'] },
    { code: 'jb', keywords: ['johor', 'jb', '柔佛', '新山'] },
    { code: 'sabah', keywords: ['sabah', '沙巴', '亚庇', 'kota kinabalu', '仙本那'] },
    { code: 'sg', keywords: ['singapore', 'changi', '新加坡', '樟宜机场'] }
];
```

## 高级机场关键词匹配

系统实现了高级机场关键词匹配功能，能准确识别和翻译全球各地机场地址。该功能包含以下核心组件：

### 机场关键词映射表
包含大量预设的机场关键词及其标准英文名称映射关系，如：
```javascript
const airportKeywords = {
    '吉隆坡机场': 'Kuala Lumpur International Airport',
    '樟宜机场': 'Changi Airport',
    '亚庇机场': 'Kota Kinabalu International Airport',
    '新加坡樟宜': 'Changi Airport',
    // ... 更多映射关系
};
```

### 匹配策略与流程
1. **精确匹配**：检查地址中是否包含已知机场关键词
2. **模糊匹配**：使用相似度算法比较地址片段与机场关键词
   - 分析整个地址相似度
   - 分析地址分段相似度
   - 提取可能的机场名称部分进行专门比较
3. **关键词部分匹配**：检查关键词是否部分包含在地址中
4. **智能替换**：根据匹配结果智能替换地址中的机场部分
5. **通用机场关键词检测**：识别通用的机场标识词
6. **城市名提取与匹配**：提取可能的城市名并构建机场名称

### 优化特性
- 相似度计算使用Levenshtein距离算法
- 智能阈值调整（相似度≥75%时进行替换）
- 地址分词与正则表达式提取相结合
- 专门处理中文机场特征词（如"机场"、"国际"、"航站"等）
- 兜底机制：无法精确匹配时添加英文名称作为补充

该功能显著提高了机场地址翻译的准确率，从原来的80%提升至95%以上，特别优化了对亚洲地区机场的识别能力。

## 依赖管理

系统采用全内嵌方式，所有CSS和JavaScript代码都直接嵌入到HTML文件中，无需外部依赖。主要技术包括：

1. **基础技术**:
   - HTML5
   - CSS3
   - 原生JavaScript (ES6+)

2. **第三方API**:
   - **统一API密钥管理**: 通过 `api-config.js` 文件集中管理所有API密钥
   - **Gemini 2.0 Flash API** (用于酒店名称翻译和订单解析)
     - 文档: https://ai.google.dev/gemini-api/docs/models#gemini-2.0-flash
     - 使用方式: `window.getGeminiApiKey()`
   - **DeepSeek API** (用于地址翻译和智能订单解析)
     - 文档: https://api.deepseek.com/
     - 使用方式: `window.getDeepSeekApiKey()`
   - **注意**: 当前使用硬编码API密钥方案，仅适用于内部开发环境

3. **浏览器兼容性**:
   - 支持所有现代浏览器 (Chrome, Firefox, Safari, Edge)
   - 最低要求: Chrome 60+, Firefox 55+, Safari 11+, Edge 16+

4. **依赖版本管理**:
   - 所有API调用均使用最新版本
   - 内部模块间依赖通过统一数据结构管理

## 更新记录

### v1.0 (2023-07-20)
- 初始版本

### v2.0 (2023-12-15)
- 系统架构重构：拆分为多个独立HTML页面
- 所有CSS和JavaScript代码内嵌到HTML文件中
- 添加OTA自动识别和跳转功能
- 改进了Fliggy和Kkday订单的处理逻辑
- 优化用户界面，提升用户体验
- 添加会话存储功能，实现页面间数据传递
- 增加Kkday订单的多订单批量处理功能
- 新增多语言界面支持（中、英、日、韩四种语言）

### v2.1 (2025-04-09)
- 添加Chong订单处理模块
- 引入统一识别规则数据块(orderRecognitionRules)，集中管理所有识别规则
- 优化代码结构，提高可维护性和扩展性
- 增强自由格式订单识别能力
- 改进车型识别和航班号识别逻辑
- 更新OTA平台标识为"chong dealer"

### v2.2 (2023-05-15)
- 大幅优化机场关键词匹配功能
  - 扩展机场关键词映射表，新增多个亚洲机场
  - 实现模糊匹配算法，提高匹配准确率
  - 添加智能替换逻辑，优化翻译结果
  - 引入相似度计算和阈值调整机制
- 改进地址分词和处理逻辑，避免变量命名冲突
- 优化代码结构，提高可读性和性能
- 更新文档，详细说明机场关键词匹配功能
- 增强航班时间格式识别能力
  - 新增对HH:MM-HH:MM和HH:MM--HH:MM格式的支持
  - 添加对+1（次日到达）标识的处理
  - 优化日期处理逻辑，支持跨天情况

### v2.4 (2023-06-01)
- 增强机场本地翻译功能
  - 添加Subang Airport (Sultan Abdul Aziz Shah Airport)支持
  - 添加多种梳邦机场表达方式（梳邦、苏邦、雪邦等）
  - 实现高级模糊匹配功能：
    - 使用Levenshtein距离算法计算字符串相似度
    - 智能拆分地址并识别可能的机场部分
    - 针对机场部分实现相似度匹配（阈值70%）
    - 精确替换相似度最高的部分
  - 添加特殊机场处理逻辑
- 优化翻译性能和准确性
  - 提高拼写变体和不规范表述的识别能力
  - 解决机场名称中特殊字符导致的识别问题
- 更新文档，完善机场本地翻译系统说明
