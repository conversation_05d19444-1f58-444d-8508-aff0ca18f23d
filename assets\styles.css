/* OTA订单转换器通用样式 */

/* 基础样式 */
:root {
    --primary-color: #3b82f6;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --gray-color: #6b7280;
    --border-color: #e5e7eb;
    --background-color: #f9fafb;
    --card-background: #ffffff;
    --text-color: #1f2937;
    --text-light: #4b5563;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
    background-color: var(--background-color);
    color: var(--text-color);
    line-height: 1.5;
    margin: 0;
    padding: 0;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

/* 通知样式 */
.notification {
    position: fixed;
    top: 1rem;
    right: 1rem;
    background-color: var(--success-color);
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 0.375rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transform: translateY(-100%);
    opacity: 0;
    transition: transform 0.3s, opacity 0.3s;
    z-index: 1000;
}

.notification.show {
    transform: translateY(0);
    opacity: 1;
}

/* 头部样式 */
header {
    background-color: var(--card-background);
    border-bottom: 1px solid var(--border-color);
    padding: 1rem 0;
    margin-bottom: 2rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo-container {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.logo-container img {
    height: 2.5rem;
}

.logo-container h1 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
}

.lang-selector {
    margin-left: auto;
}

/* 语言选择器样式 */
.language-selector {
    width: auto; 
    min-width: 120px;
}

/* 卡片样式 */
.card {
    background-color: var(--card-background);
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    margin-bottom: 1.5rem;
    overflow: hidden;
}

.card-header {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-title {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
}

.card-body {
    padding: 1.5rem;
}

/* 表单样式 */
.form-group {
    margin-bottom: 1rem;
}

.form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.form-input {
    width: 100%;
    padding: 0.625rem;
    border: 1px solid var(--border-color);
    border-radius: 0.375rem;
    font-size: 1rem;
    transition: border-color 0.15s;
}

.form-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

textarea.form-input {
    min-height: 150px;
    /* 添加前缀以支持旧版Edge浏览器 */
    -ms-resize: vertical;
    resize: vertical;
}

/* 按钮样式 */
.btn {
    display: inline-block;
    padding: 0.625rem 1.25rem;
    font-size: 1rem;
    font-weight: 500;
    /* 添加前缀以支持旧版Edge浏览器 */
    -ms-text-align: center;
    text-align: center;
    border: none;
    border-radius: 0.375rem;
    cursor: pointer;
    transition: background-color 0.15s, transform 0.1s;
    text-decoration: none;
}

.btn:active {
    transform: translateY(1px);
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background-color: #2563eb;
}

.btn-success {
    background-color: var(--success-color);
    color: white;
}

.btn-success:hover {
    background-color: #059669;
}

.btn-warning {
    background-color: var(--warning-color);
    color: white;
}

.btn-warning:hover {
    background-color: #d97706;
}

.btn-danger {
    background-color: var(--danger-color);
    color: white;
}

.btn-danger:hover {
    background-color: #dc2626;
}

.btn-gray {
    background-color: var(--gray-color);
    color: white;
}

.btn-gray:hover {
    background-color: #4b5563;
}

.button-group {
    display: flex;
    gap: 0.5rem;
    margin-top: 1rem;
}

.no-margin-top {
    margin-top: 0;
}

.full-width {
    width: 100%;
}

/* 订单项样式 */
.order-item {
    background-color: var(--card-background);
    border: 1px solid var(--border-color);
    border-radius: 0.375rem;
    margin-bottom: 1rem;
    overflow: hidden;
    cursor: pointer;
    transition: transform 0.15s, box-shadow 0.15s;
}

.order-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.order-item-header {
    padding: 0.75rem 1rem;
    background-color: rgba(0, 0, 0, 0.02);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.order-item-body {
    padding: 0.75rem 1rem;
}

.order-item-footer {
    padding: 0.5rem 1rem;
    background-color: rgba(0, 0, 0, 0.02);
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    font-size: 0.875rem;
    color: var(--text-light);
}

.order-badge {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    font-weight: 500;
    border-radius: 9999px;
}

/* 隐藏元素 */
.hidden {
    display: none !important;
}

/* 页脚样式 */
footer {
    /* 添加前缀以支持旧版Edge浏览器 */
    -ms-text-align: center;
    text-align: center;
    padding: 2rem 0;
    margin-top: 3rem;
    color: var(--text-light);
    font-size: 0.875rem;
}

.footer-link {
    color: #3b82f6; 
    text-decoration: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: 1rem;
    }
    
    .lang-selector {
        margin-left: 0;
    }
    
    .button-group {
        flex-direction: column;
    }
}

/* OTA特定样式 */
/* Chong Dealer特定样式 */
.chong-header {
    background-color: #f0f8ff;
    border-bottom: 1px solid var(--border-color);
}

.chong-title {
    color: #0066cc;
}

.badge-pickup {
    background-color: rgba(59, 130, 246, 0.1);
    color: var(--primary-color);
}

.badge-dropoff {
    background-color: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.badge-charter {
    background-color: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
}

/* Jing Ge特定样式 */
.jingge-header {
    background-color: #f0fff4;
    border-bottom: 1px solid var(--border-color);
}

.jingge-title {
    color: #059669;
}

/* Fliggy特定样式 */
.fliggy-header {
    background-color: #fffbeb;
    border-bottom: 1px solid var(--border-color);
}

.fliggy-title {
    color: #d97706;
}

/* KKDay特定样式 */
.kkday-header {
    background-color: #fff1f2;
    border-bottom: 1px solid var(--border-color);
}

.kkday-title {
    color: #be123c;
}
