<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>GoMyHire 进单页面</title>
  <style>
    body { font-family: Arial, sans-serif; background-color: #f5f7fa; color: #333; padding: 20px; }
    .container { max-width: 800px; margin: 0 auto; background: #fff; padding: 24px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); }
    h1 { font-size: 24px; margin-bottom: 16px; }
    label { display: block; margin-top: 12px; font-weight: bold; }
    select, input, textarea { width: 100%; padding: 8px; margin-top: 4px; border: 1px solid #ccc; border-radius: 4px; }
    button { margin-top: 16px; padding: 10px 20px; background-color: #007bff; border: none; border-radius: 4px; color: #fff; font-size: 16px; cursor: pointer; }
    button:disabled { background-color: #a0c5f2; cursor: not-allowed; }
    .message { margin-top: 12px; }
  </style>
</head>
<body>
  <div class="container">
    <h1>GoMyHire 订单录入</h1>
    <form id="order-form">
      <label for="backend-user">操作员</label>
      <select id="backend-user"><option>加载中...</option></select>

      <label for="sub-category">子分类</label>
      <select id="sub-category"><option>加载中...</option></select>

      <label for="car-type">车型</label>
      <select id="car-type"><option>加载中...</option></select>

      <label for="ota-ref">OTA 单号</label>
      <input type="text" id="ota-ref" placeholder="请输入 OTA 订单号" required />

      <label for="customer-name">客户姓名</label>
      <input type="text" id="customer-name" placeholder="可选" />

      <label for="pickup">上车地点</label>
      <input type="text" id="pickup" placeholder="可选" />

      <label for="destination">下车地点</label>
      <input type="text" id="destination" placeholder="可选" />

      <button type="submit" id="btn-create" disabled>创建订单</button>
    </form>

    <div class="message" id="message"></div>
  </div>

  <script>
            // 操作ID生成器
            let operationId = 1;
        
            /**
             * 自定义日志函数，统一处理日志输出
             * @param {string} level - 日志级别(DEBUG/INFO/WARN/ERROR)
             * @param {string} message - 日志消息
             * @param {object} [context] - 附加上下文信息
             */
            function log(level, message, context = {}) {
                const timestamp = new Date().toISOString();
                const currentOpId = operationId++;
                const logEntry = {
                    timestamp,
                    level,
                    operationId: currentOpId,
                    message,
                    ...context
                };
                console.log(`[GoMyHire][${timestamp}][${level}][Op-${currentOpId}] ${message}`, 
                    Object.keys(context).length > 0 ? context : '');
            }

    // 页面加载时自动获取数据，并启用提交按钮
    document.addEventListener('DOMContentLoaded', async () => {
      log('INFO', '开始初始化页面');
      document.getElementById('btn-create').disabled = true;
      await Promise.all([loadBackendUsers(), loadSubCategories(), loadCarTypes()]);
      log('INFO', '数据加载完成，启用创建按钮');
      document.getElementById('btn-create').disabled = false;
    });

    /**
     * 加载后台用户列表并填充下拉框
     * @returns {Promise<void>}
     */
    async function loadBackendUsers() {
      const sel = document.getElementById('backend-user');
      sel.innerHTML = '<option>加载中...</option>';
      const startTime = Date.now();
      log('INFO', '开始加载后台用户列表', {
        elementId: 'backend-user',
        requestUrl: 'https://staging.gomyhire.com.my/api/backend_users'
      });
      
      try {
        const res = await fetch('https://staging.gomyhire.com.my/api/backend_users?search=');
        const duration = Date.now() - startTime;
        log('DEBUG', '收到后台用户响应', {
          status: res.status,
          duration: `${duration}ms`,
          headers: Object.fromEntries(res.headers.entries())
        });
        
        const json = await res.json();
        log('DEBUG', '解析后台用户数据', {
          recordCount: json.data.length,
          sampleData: json.data.slice(0, 3) // 记录前3条样本数据
        });
        
        sel.innerHTML = '';
        json.data.forEach(u => sel.append(new Option(u.name + ' (#' + u.id + ')', u.id)));
        log('INFO', '后台用户下拉框填充完成', {
          optionsCount: json.data.length,
          duration: `${Date.now() - startTime}ms`
        });
      } catch (e) {
        log('ERROR', '加载后台用户失败', {
          error: e.message,
          stack: e.stack,
          duration: `${Date.now() - startTime}ms`
        });
        sel.innerHTML = '<option>加载失败</option>';
      }
    }

    /**
     * 加载子分类列表并填充下拉框
     * @returns {Promise<void>}
     */
    async function loadSubCategories() {
      const sel = document.getElementById('sub-category');
      sel.innerHTML = '<option>加载中...</option>';
      log('INFO', '开始加载子分类列表');
      
      try {
        const res = await fetch('https://staging.gomyhire.com.my/api/sub_category?search=');
        log('DEBUG', `收到子分类响应: 状态码 ${res.status}`);
        
        const json = await res.json();
        log('DEBUG', `解析子分类数据: ${json.data.length}条记录`);
        
        sel.innerHTML = '';
        json.data.forEach(c => sel.append(new Option(c.name + ' (#' + c.id + ')', c.id)));
        log('INFO', '子分类下拉框填充完成');
      } catch (e) {
        log('ERROR', `加载子分类失败: ${e.message}`);
        sel.innerHTML = '<option>加载失败</option>';
      }
    }

    /**
     * 加载车型列表并填充下拉框
     * @returns {Promise<void>}
     */
    async function loadCarTypes() {
      const sel = document.getElementById('car-type');
      sel.innerHTML = '<option>加载中...</option>';
      log('INFO', '开始加载车型列表');
      
      try {
        const res = await fetch('https://staging.gomyhire.com.my/api/car_types?search=');
        log('DEBUG', `收到车型响应: 状态码 ${res.status}`);
        
        const json = await res.json();
        log('DEBUG', `解析车型数据: ${json.data.length}条记录`);
        
        sel.innerHTML = '';
        json.data.forEach(ct => sel.append(new Option(ct.name + ' (#' + ct.id + ')', ct.id)));
        log('INFO', '车型下拉框填充完成');
      } catch (e) {
        log('ERROR', `加载车型失败: ${e.message}`);
        sel.innerHTML = '<option>加载失败</option>';
      }
    }

    /**
     * 验证订单表单数据
     * @param {HTMLFormElement} form - 表单元素
     * @returns {boolean} 验证是否通过
     */
    function validateForm(form) {
      const startTime = Date.now();
      const requiredFields = [
        { id: 'sub-category', name: '子分类' },
        { id: 'car-type', name: '车型' },
        { id: 'backend-user', name: '操作员' }
      ];

      const validationResults = requiredFields.map(field => {
        const value = document.getElementById(field.id).value;
        return {
          field: field.id,
          name: field.name,
          valid: !!value,
          value: value
        };
      });

      const invalidFields = validationResults
        .filter(result => !result.valid)
        .map(result => result.name);

      if (invalidFields.length > 0) {
        log('WARN', '表单验证失败', {
          operation: 'form_validation',
          invalidFields,
          validationResults,
          duration: `${Date.now() - startTime}ms`
        });
        alert(`请填写以下必填字段: ${invalidFields.join(', ')}`);
        return false;
      }

      log('DEBUG', '表单验证通过', {
        operation: 'form_validation',
        validationResults,
        duration: `${Date.now() - startTime}ms`
      });
      return true;
    }

    /**
     * 提交订单表单数据
     * @param {Event} e - 表单提交事件
     * @returns {Promise<void>}
     */
    async function submitOrder(e) {
      e.preventDefault();
      const startTime = Date.now();
      const operation = 'order_submission';
      
      log('INFO', '开始处理订单提交', {
        operation,
        formId: 'order-form'
      });

      // 验证表单
      if (!validateForm(document.getElementById('order-form'))) {
        log('WARN', '订单提交中止: 表单验证失败', {
          operation,
          status: 'validation_failed',
          duration: `${Date.now() - startTime}ms`
        });
        return;
      }
      
      const btn = document.getElementById('btn-create');
      btn.disabled = true;
      btn.textContent = '提交中...';

      // 收集表单数据
      const payload = {
        sub_category_id: parseInt(document.getElementById('sub-category').value),
        ota_reference_number: document.getElementById('ota-ref').value,
        car_type_id: parseInt(document.getElementById('car-type').value),
        incharge_by_backend_user_id: parseInt(document.getElementById('backend-user').value),
        customer_name: document.getElementById('customer-name').value,
        pickup: document.getElementById('pickup').value,
        destination: document.getElementById('destination').value
      };
      
      log('DEBUG', '收集订单数据', {
        operation,
        payload,
        fieldValues: {
          sub_category: document.getElementById('sub-category').value,
          car_type: document.getElementById('car-type').value,
          backend_user: document.getElementById('backend-user').value
        }
      });

      try {
        log('INFO', '准备发送订单请求', {
          operation,
          apiEndpoint: 'https://staging.gomyhire.com.my/api/create_order',
          requestConfig: {
            method: 'POST',
            headers: {'Content-Type':'application/json'},
            bodySize: JSON.stringify(payload).length
          }
        });
        
        const res = await fetch('https://staging.gomyhire.com.my/api/create_order', {
          method: 'POST', 
          headers: {'Content-Type':'application/json'},
          body: JSON.stringify(payload)
        });
        
        const responseTime = Date.now() - startTime;
        log('DEBUG', '收到订单响应', {
          operation,
          status: res.status,
          responseTime: `${responseTime}ms`,
          headers: Object.fromEntries(res.headers.entries())
        });
        
        const result = await res.json();
        log('DEBUG', '解析订单响应', {
          operation,
          responseData: result,
          validationErrors: result.data?.validation_error || null
        });
        
        if (result.status) {
          log('INFO', '订单创建成功', {
            operation,
            orderId: result.data.order_id,
            totalTime: `${Date.now() - startTime}ms`
          });
          document.getElementById('message').textContent = '订单创建成功，ID：' + result.data.order_id;
        } else {
          log('ERROR', '订单创建失败', {
            operation,
            validationErrors: result.data.validation_error,
            totalTime: `${Date.now() - startTime}ms`
          });
          document.getElementById('message').textContent = '创建失败：' + JSON.stringify(result.data.validation_error);
        }
      } catch (e) {
        log('ERROR', '订单提交异常', {
          operation,
          errorType: e.name,
          errorMessage: e.message,
          stack: e.stack,
          totalTime: `${Date.now() - startTime}ms`
        });
        document.getElementById('message').textContent = '网络错误，创建失败';
      } finally {
        btn.disabled = false;
        btn.textContent = '创建订单';
        log('INFO', '订单提交流程完成', {
          operation,
          status: 'completed',
          totalTime: `${Date.now() - startTime}ms`
        });
      }
    }

    // 绑定表单提交事件
    document.getElementById('order-form').addEventListener('submit', submitOrder);
  </script>
</body>
</html>