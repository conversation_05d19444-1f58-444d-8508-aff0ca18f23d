<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OTA Order Converter - Kkday订单处理</title>
    <style>
        /* 基础样式 */
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            background-color: #f9fafb;
            color: #111827;
            margin: 0;
            padding: 0;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 1rem;
            width: 100%;
            box-sizing: border-box;
        }
        
        header {
            background-color: white;
            border-bottom: 1px solid #e5e7eb;
            padding: 1rem 0;
        }
        
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo-container {
            display: flex;
            align-items: center;
        }
        
        .logo-container img {
            height: 2rem;
            margin-right: 0.75rem;
        }
        
        h1 {
            font-size: 1.25rem;
            font-weight: 500;
            margin: 0;
        }
        
        main {
            flex: 1;
            padding: 1.5rem 0;
        }
        
        .card {
            background-color: white;
            border-radius: 0.5rem;
            border: 1px solid #e5e7eb;
            overflow: hidden;
            margin-bottom: 1.5rem;
        }
        
        .card-header {
            padding: 1.25rem;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .card-title {
            font-size: 1.125rem;
            font-weight: 500;
            margin: 0;
        }
        
        .card-body {
            padding: 1.25rem;
        }
        
        .form-group {
            margin-bottom: 1rem;
        }
        
        .form-label {
            display: block;
            font-size: 0.875rem;
            font-weight: 500;
            color: #374151;
            margin-bottom: 0.25rem;
        }
        
        .form-input, textarea, select {
            width: 100%;
            padding: 0.5rem 0.75rem;
            border: 1px solid #d1d5db;
            border-radius: 0.375rem;
            background-color: #fff;
            color: #111827;
            font-size: 1rem;
            line-height: 1.5;
            transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
        }
        
        .form-input:focus, textarea:focus, select:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.25);
        }
        
        .form-input[readonly], .form-input:disabled {
            background-color: #f9fafb;
            cursor: not-allowed;
        }
        
        textarea {
            min-height: 150px;
            resize: vertical;
        }
        
        .btn {
            display: inline-block;
            font-weight: 500;
            text-align: center;
            white-space: nowrap;
            vertical-align: middle;
            -webkit-user-select: none;
            user-select: none;
            border: 1px solid transparent;
            padding: 0.5rem 1rem;
            font-size: 1rem;
            line-height: 1.5;
            border-radius: 0.375rem;
            transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
            cursor: pointer;
        }
        
        .btn-primary {
            color: #fff;
            background-color: #3b82f6;
            border-color: #3b82f6;
        }
        
        .btn-primary:hover {
            background-color: #2563eb;
            border-color: #2563eb;
        }
        
        .btn-gray {
            color: #fff;
            background-color: #6b7280;
            border-color: #6b7280;
        }
        
        .btn-gray:hover {
            background-color: #4b5563;
            border-color: #4b5563;
        }
        
        .btn-green {
            color: #fff;
            background-color: #10b981;
            border-color: #10b981;
        }
        
        .btn-green:hover {
            background-color: #059669;
            border-color: #059669;
        }
        
        .button-group {
            display: flex;
            gap: 0.5rem;
            margin-top: 1rem;
        }
        
        .flex-1 {
            flex: 1;
        }
        
        .hidden {
            display: none;
        }
        
        .grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 1.5rem;
        }
        
        @media (min-width: 1024px) {
            .grid {
                grid-template-columns: 1fr 1fr;
            }
        }
        
        .form-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 1rem;
        }
        
        @media (min-width: 768px) {
            .form-grid {
                grid-template-columns: 1fr 1fr;
            }
        }
        
        .col-span-2 {
            grid-column: span 1;
        }
        
        @media (min-width: 768px) {
            .col-span-2 {
                grid-column: span 2;
            }
        }
        
        footer {
            margin-top: auto;
            padding: 1rem 0;
            background-color: white;
            border-top: 1px solid #e5e7eb;
            text-align: center;
            font-size: 0.875rem;
            color: #6b7280;
        }
        
        .notification {
            position: fixed;
            bottom: 1rem;
            right: 1rem;
            background-color: #10b981;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 0.375rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            opacity: 0;
            transition: opacity 0.3s ease-in-out;
            z-index: 50;
        }
        
        .notification.show {
            opacity: 1;
        }
        
        .order-item {
            border: 1px solid #e5e7eb;
            border-radius: 0.5rem;
            padding: 1rem;
            transition: transform 0.2s ease;
        }
        
        .order-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }
    </style>
</head>
<body>
    <div class="notification" id="notification"></div>
    
    <header>
        <div class="container header-content">
            <div class="logo-container">
                <img src="images/logo.svg" alt="Logo">
                <h1>OTA Order Converter - Kkday订单处理</h1>
            </div>
            <a href="index.html" class="btn btn-gray">返回首页</a>
        </div>
    </header>

    <main class="container">
        <div class="grid">
            <!-- 输入部分 -->
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title" data-i18n="kkday-original-order">Kkday原始订单</h2>
                    <div class="lang-selector">
                        <select id="language-selector" class="form-input" style="width: auto; min-width: 120px;" aria-label="选择语言 Select language">
                            <option value="zh">中文</option>
                            <option value="en">English</option>
                            <option value="jp">日本語</option>
                            <option value="ko">한국어</option>
                        </select>
                    </div>
                </div>
                <div class="card-body">
                    <div class="form-group">
                        <label for="raw-order" class="form-label" data-i18n="original-order-data">原始订单数据</label>
                        <textarea id="raw-order" data-i18n-placeholder="paste-kkday-order-data" placeholder="粘贴Kkday原始订单数据..."></textarea>
                    </div>
                    <div class="button-group">
                        <button id="convert-btn" class="btn btn-primary flex-1" data-i18n="process-order">处理订单</button>
                        <button id="reset-btn" class="btn btn-gray" data-i18n="reset">重置</button>
                    </div>
                </div>
            </div>

            <!-- 输出部分 -->
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title" data-i18n="standardized-order">标准化订单</h2>
                    <button id="copy-output" class="btn btn-gray" data-i18n="copy-output">复制</button>
                </div>
                <div class="card-body">
                    <form id="order-form">
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="ota" class="form-label" data-i18n="ota-platform">OTA平台</label>
                                <input type="text" id="ota" class="form-input" readonly>
                            </div>
                            <div class="form-group">
                                <label for="ota-reference" class="form-label" data-i18n="order-number">OTA订单号</label>
                                <input type="text" id="ota-reference" class="form-input" readonly>
                            </div>
                            <div class="form-group">
                                <label for="price" class="form-label" data-i18n="price">价格</label>
                                <input type="text" id="price" class="form-input" readonly>
                            </div>
                            <div class="form-group">
                                <label for="name" class="form-label" data-i18n="passenger-name">乘客姓名</label>
                                <input type="text" id="name" class="form-input" readonly>
                            </div>
                            <div class="form-group">
                                <label for="phone" class="form-label" data-i18n="phone">电话</label>
                                <input type="text" id="phone" class="form-input" readonly>
                            </div>
                            <div class="form-group">
                                <label for="email" class="form-label" data-i18n="email">邮箱</label>
                                <input type="email" id="email" class="form-input" readonly>
                            </div>
                            <div class="form-group">
                                <label for="flight-number" class="form-label" data-i18n="flight-number">航班号</label>
                                <input type="text" id="flight-number" class="form-input" readonly>
                            </div>
                            <div class="form-group">
                                <label for="pickup-datetime" class="form-label" data-i18n="pickup-time">接机时间</label>
                                <input type="text" id="pickup-datetime" class="form-input" readonly>
                            </div>
                            <div class="form-group col-span-2">
                                <label for="pickup-address" class="form-label" data-i18n="pickup-address">接机地址</label>
                                <input type="text" id="pickup-address" class="form-input" readonly>
                            </div>
                            <div class="form-group col-span-2">
                                <label for="dropoff-address" class="form-label" data-i18n="dropoff-address">送机地址</label>
                                <input type="text" id="dropoff-address" class="form-input" readonly>
                            </div>
                            <div class="form-group">
                                <label for="car-type" class="form-label" data-i18n="car-type">车型</label>
                                <input type="text" id="car-type" class="form-input" readonly>
                            </div>
                            <div class="form-group">
                                <label for="passenger-number" class="form-label" data-i18n="passenger-count">乘客人数</label>
                                <input type="number" id="passenger-number" class="form-input" readonly>
                            </div>
                            <div class="form-group">
                                <label for="luggage-number" class="form-label" data-i18n="luggage-count">行李数量</label>
                                <input type="number" id="luggage-number" class="form-input" readonly>
                            </div>
                            <div class="form-group">
                                <label for="language" class="form-label" data-i18n="language">语言</label>
                                <input type="text" id="language" class="form-input" readonly>
                            </div>
                            <div class="form-group">
                                <label for="category" class="form-label" data-i18n="order-type">类别</label>
                                <select id="category" class="form-input" disabled>
                                    <option value="airport" data-i18n="airport-pickup">机场接送</option>
                                    <option value="charter" data-i18n="charter">包车</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="subcategory" class="form-label">子类别</label>
                                <select id="subcategory" class="form-input" disabled>
                                    <option value="pickup">接机</option>
                                    <option value="dropoff">送机</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="driving-region" class="form-label" data-i18n="driving-region">驾驶区域</label>
                                <select id="driving-region" class="form-input" disabled>
                                    <option value="kl">吉隆坡</option>
                                    <option value="penang">槟城</option>
                                    <option value="sg">新加坡</option>
                                    <option value="jb">新山</option>
                                    <option value="sabah">沙巴</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="driver" class="form-label" data-i18n="driver-count">司机数量</label>
                                <input type="number" id="driver" class="form-input" value="1" readonly>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="remark" class="form-label" data-i18n="remarks">备注</label>
                            <textarea id="remark" rows="2" class="form-input" readonly></textarea>
                        </div>
                        <div class="button-group">
                            <button type="button" id="edit-btn" class="btn btn-gray flex-1" data-i18n="enable-edit">编辑</button>
                            <button type="button" id="save-btn" class="btn btn-green flex-1 hidden" data-i18n="save-changes">保存</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </main>

    <!-- 多订单结果容器 -->
    <div id="multi-orders-container" class="card hidden container">
        <div class="card-header">
            <h2 class="card-title" data-i18n="multiple-orders-detected">已识别到的多个订单</h2>
        </div>
        <div id="orders-list" class="card-body" style="display: grid; grid-template-columns: 1fr; gap: 1rem;">
            <!-- 订单条目将在这里动态添加 -->
        </div>
    </div>

    <footer>
        <div class="container">
            Built with <a href="https://flowith.net" target="_blank" rel="noopener" style="color: #3b82f6; text-decoration: none;">Flowith Oracle</a>.
        </div>
    </footer>

    <script>
        // 多语言支持
        const i18n = {
            zh: {
                // 页面标题
                "kkday-original-order": "Kkday原始订单",
                "standardized-order": "标准化订单",
                "multiple-orders-detected": "已识别到的多个订单",
                // 表单标签和占位符
                "original-order-data": "原始订单数据",
                "paste-kkday-order-data": "粘贴Kkday原始订单数据...",
                "ota-platform": "OTA平台",
                "order-number": "订单编号",
                "price": "价格",
                "passenger-name": "乘客姓名",
                "phone": "电话",
                "email": "邮箱",
                "flight-number": "航班号",
                "pickup-time": "接机时间",
                "pickup-address": "接机地址",
                "dropoff-address": "送达地址",
                "car-type": "车型",
                "luggage-count": "行李数量",
                "passenger-count": "乘客人数",
                "language": "语言",
                "order-type": "订单类型",
                "driving-region": "驾驶区域",
                "driver-count": "司机数量",
                "remarks": "备注",
                // 按钮
                "process-order": "处理订单",
                "reset": "重置",
                "enable-edit": "启用编辑",
                "save-changes": "保存更改",
                "copy-output": "复制输出",
                "view-details": "查看详细",
                "copy": "复制",
                "back-to-home": "返回首页",
                // 通知
                "please-enter-data": "请输入原始订单数据",
                "data-copied": "数据已复制到剪贴板",
                "edit-enabled": "已启用编辑模式",
                "changes-saved": "更改已保存",
                "multiple-orders-found": "检测到多个订单，正在处理...",
                // 字段值
                "airport-pickup": "机场接机",
                "airport-dropoff": "机场送机",
                "charter": "包车服务"
            },
            en: {
                // Page titles
                "kkday-original-order": "Kkday Original Order",
                "standardized-order": "Standardized Order",
                "multiple-orders-detected": "Multiple Orders Detected",
                // Form labels and placeholders
                "original-order-data": "Original Order Data",
                "paste-kkday-order-data": "Paste Kkday original order data...",
                "ota-platform": "OTA Platform",
                "order-number": "Order Number",
                "price": "Price",
                "passenger-name": "Passenger Name",
                "phone": "Phone",
                "email": "Email",
                "flight-number": "Flight Number",
                "pickup-time": "Pickup Time",
                "pickup-address": "Pickup Address",
                "dropoff-address": "Dropoff Address",
                "car-type": "Car Type",
                "luggage-count": "Luggage Count",
                "passenger-count": "Passenger Count",
                "language": "Language",
                "order-type": "Order Type",
                "driving-region": "Driving Region",
                "driver-count": "Driver Count",
                "remarks": "Remarks",
                // Buttons
                "process-order": "Process Order",
                "reset": "Reset",
                "enable-edit": "Enable Edit",
                "save-changes": "Save Changes",
                "copy-output": "Copy Output",
                "view-details": "View Details",
                "copy": "Copy",
                "back-to-home": "Back to Home",
                // Notifications
                "please-enter-data": "Please enter original order data",
                "data-copied": "Data copied to clipboard",
                "edit-enabled": "Edit mode enabled",
                "changes-saved": "Changes saved",
                "multiple-orders-found": "Multiple orders detected, processing...",
                // Field values
                "airport-pickup": "Airport Pickup",
                "airport-dropoff": "Airport Dropoff",
                "charter": "Charter Service"
            },
            jp: {
                // ページタイトル
                "kkday-original-order": "Kkday元注文",
                "standardized-order": "標準化注文",
                "multiple-orders-detected": "検出された複数の注文",
                // フォームラベルとプレースホルダー
                "original-order-data": "元注文データ",
                "paste-kkday-order-data": "Kkdayの元注文データを貼り付け...",
                "ota-platform": "OTAプラットフォーム",
                "order-number": "注文番号",
                "price": "価格",
                "passenger-name": "乗客名",
                "phone": "電話番号",
                "email": "メール",
                "flight-number": "フライト番号",
                "pickup-time": "送迎時間",
                "pickup-address": "ピックアップ住所",
                "dropoff-address": "目的地住所",
                "car-type": "車種",
                "luggage-count": "荷物数",
                "passenger-count": "乗客数",
                "language": "言語",
                "order-type": "注文タイプ",
                "driving-region": "運転地域",
                "driver-count": "ドライバー数",
                "remarks": "備考",
                // ボタン
                "process-order": "注文処理",
                "reset": "リセット",
                "enable-edit": "編集を有効化",
                "save-changes": "変更を保存",
                "copy-output": "出力をコピー",
                "view-details": "詳細を表示",
                "copy": "コピー",
                "back-to-home": "ホームに戻る",
                // 通知
                "please-enter-data": "元注文データを入力してください",
                "data-copied": "データがクリップボードにコピーされました",
                "edit-enabled": "編集モードが有効になりました",
                "changes-saved": "変更が保存されました",
                "multiple-orders-found": "複数の注文が検出されました、処理中...",
                // フィールド値
                "airport-pickup": "空港送迎",
                "airport-dropoff": "空港へ送り",
                "charter": "チャーターサービス"
            },
            ko: {
                // 페이지 제목
                "kkday-original-order": "Kkday 원본 주문",
                "standardized-order": "표준화된 주문",
                "multiple-orders-detected": "감지된 다중 주문",
                // 폼 라벨 및 플레이스홀더
                "original-order-data": "원본 주문 데이터",
                "paste-kkday-order-data": "Kkday 원본 주문 데이터 붙여넣기...",
                "ota-platform": "OTA 플랫폼",
                "order-number": "주문 번호",
                "price": "가격",
                "passenger-name": "승객 이름",
                "phone": "전화번호",
                "email": "이메일",
                "flight-number": "항공편 번호",
                "pickup-time": "픽업 시간",
                "pickup-address": "픽업 주소",
                "dropoff-address": "하차 주소",
                "car-type": "차량 유형",
                "luggage-count": "수하물 개수",
                "passenger-count": "승객 수",
                "language": "언어",
                "order-type": "주문 유형",
                "driving-region": "운전 지역",
                "driver-count": "운전자 수",
                "remarks": "비고",
                // 버튼
                "process-order": "주문 처리",
                "reset": "재설정",
                "enable-edit": "편집 활성화",
                "save-changes": "변경사항 저장",
                "copy-output": "출력 복사",
                "view-details": "상세 보기",
                "copy": "복사",
                "back-to-home": "홈으로 돌아가기",
                // 알림
                "please-enter-data": "원본 주문 데이터를 입력하세요",
                "data-copied": "데이터가 클립보드에 복사되었습니다",
                "edit-enabled": "편집 모드가 활성화되었습니다",
                "changes-saved": "변경사항이 저장되었습니다",
                "multiple-orders-found": "다중 주문이 감지되었습니다, 처리 중...",
                // 필드 값
                "airport-pickup": "공항 픽업",
                "airport-dropoff": "공항 드랍",
                "charter": "전세 서비스"
            }
        };

        // 更新所有UI文本
        function updateUILanguage(lang) {
            // 更新标题
            document.title = `OTA Order Converter - ${i18n[lang]["kkday-original-order"]}`;
            
            // 更新data-i18n属性的元素
            document.querySelectorAll('[data-i18n]').forEach(element => {
                const key = element.getAttribute('data-i18n');
                if (i18n[lang][key]) {
                    element.textContent = i18n[lang][key];
                }
            });
            
            // 更新占位符
            document.querySelectorAll('[data-i18n-placeholder]').forEach(element => {
                const key = element.getAttribute('data-i18n-placeholder');
                if (i18n[lang][key]) {
                    element.placeholder = i18n[lang][key];
                }
            });
            
            // 保存当前语言到localStorage
            localStorage.setItem('preferred-language', lang);
        }

        // 定义Kkday的识别规则
        const kkdayRule = {
            id: 2,
            provider: 'Kkday',
            identification: 'kkday, 订单编号, 私人机场接送, 成本金额',
            fieldMappings: [
                { field: 'reference', pattern: '(?:订单编号|Booking Reference Number) : (\\w+)' },
                { field: 'name', pattern: '(?:订购人|Buyer)：([^\\n]+)' },
                { field: 'phone', pattern: '(?:订购人电话|Buyer\'s Phone Number)：([^\\n]+)' },
                { field: 'email', pattern: '(?:订购人E-mail|Buyer\'s e-mail address)：([^\\n]+)' },
                { field: 'flight', pattern: '(?:航班编号|Flight no\\.)\\s*([A-Za-z0-9\\-\\s]+)' },
                { field: 'pickup-datetime', pattern: '(?:Date & Time\\s*(\\d{4}-\\d{2}-\\d{2}\\s+\\d{2}:\\d{2})|(?:航班到达日期及时间|Date & Time)\\s*(\\d{4}-\\d{2}-\\d{2}\\s+\\d{2}:\\d{2})|(?:接駁日期|Shuttle service date)\\s*(\\d{4}-\\d{2}-\\d{2})(?:[\\s\\S]*?(?:接駁時間|Session)：\\s*(\\d{2}:\\d{2}))?)' },
                { field: 'pickup-address', pattern: '(?:上车地点|航厦|Terminal No\\.)\\s*([^\\n]+)' },
                { field: 'dropoff-address', pattern: '(?:吉隆坡市区 至|下车地点|Drop off location)\\s*([^\\n]+)' },
                { field: 'car-type', pattern: '(?:车辆：|车辆类型：|Vehicle：)([^\\n]+)' },
                { field: 'luggage', pattern: '(?:手提|托运行李数量|Carry-on|Checked bags)\\s*(\\d+)' },
                { field: 'price', pattern: '(?:成本金额|Cost)MYR\\s*(\\d+(?:[.,]\\d+)?)' },
                { field: 'remark', pattern: '(?:订单预计确认时间|Estimated Order Confirmation Time)：(?:\\d{4}-\\d{2}-\\d{2}\\s+\\d{2}:\\d{2}\\s*\\(GMT\\+\\d+\\)\\s*)?((?:.|\\n)*?)(?=(?:成本金额|Cost))' },
                { field: 'passengers', pattern: '(?:成人人数|商品数量|Number of adults|Quantity|人\\s*[Xx]|Car\\s*[Xx])\\s*(\\d+)' }
            ]
        };

        // DOM元素引用
        const rawOrderTextarea = document.getElementById('raw-order');
        const convertBtn = document.getElementById('convert-btn');
        const resetBtn = document.getElementById('reset-btn');
        const editBtn = document.getElementById('edit-btn');
        const saveBtn = document.getElementById('save-btn');
        const copyOutputBtn = document.getElementById('copy-output');
        const formInputs = document.querySelectorAll('#order-form input, #order-form select, #order-form textarea');
        const notification = document.getElementById('notification');
        const multiOrdersContainer = document.getElementById('multi-orders-container');
        const ordersList = document.getElementById('orders-list');
        const languageSelector = document.getElementById('language-selector');

        // 转换订单主函数
        function convertOrder() {
            // 获取原始订单文本
            const rawOrderText = rawOrderTextarea.value.trim();
            
            if (!rawOrderText) {
                const currentLang = languageSelector.value;
                showNotification(i18n[currentLang]['please-enter-data']);
                return;
            }
            
            // 检查是否有多个订单
            const orderRegex = /(?:订单编号|Booking Reference Number) : \w+/g;
            const orderMatches = [...rawOrderText.matchAll(orderRegex)];
            
            if (orderMatches.length > 1) {
                // 多订单处理
                const currentLang = languageSelector.value;
                showNotification(i18n[currentLang]['multiple-orders-found']);
                processMultipleOrders(rawOrderText);
                return;
            }
            
            // 单订单处理
            processSingleOrder(rawOrderText);
        }

        // 处理单个订单
        async function processSingleOrder(rawOrderText) {
            // 显示标准订单表单，隐藏多订单容器
            document.querySelector('.grid').classList.remove('hidden');
            multiOrdersContainer.classList.add('hidden');
            
            // 提取订单数据
            const orderData = extractOrderData(rawOrderText);
            
            // 处理pickup和dropoff地址翻译
            orderData['pickup-address'] = await processAddress(orderData['pickup-address']);
            orderData['dropoff-address'] = await processAddress(orderData['dropoff-address']);
            
            // 填充表单
            fillOrderForm(orderData);
        }

        // 处理多个订单
        function processMultipleOrders(rawOrderText) {
            // 隐藏标准订单表单，显示多订单容器
            document.querySelector('.grid').classList.add('hidden');
            multiOrdersContainer.classList.remove('hidden');
            
            // 清空订单列表
            ordersList.innerHTML = '';
            
            // 分割多个订单
            const orders = splitOrders(rawOrderText);
            
            // 处理每个订单
            orders.forEach(async (orderText, index) => {
                const orderData = extractOrderData(orderText);
                
                // 处理pickup和dropoff地址翻译
                orderData['pickup-address'] = await processAddress(orderData['pickup-address']);
                orderData['dropoff-address'] = await processAddress(orderData['dropoff-address']);
                
                const orderElement = createOrderElement(orderData, orderText, index + 1);
                ordersList.appendChild(orderElement);
            });
        }

        // 分割多个订单
        function splitOrders(rawOrderText) {
            // 使用"订单编号："或"Booking Reference Number"作为分割点
            const orderRegex = /((?:订单编号|Booking Reference Number) : \w+[\s\S]*?)(?=(?:订单编号|Booking Reference Number) : \w+|$)/g;
            const orders = [];
            let match;
            
            while ((match = orderRegex.exec(rawOrderText)) !== null) {
                orders.push(match[1].trim());
            }
            
            return orders;
        }

        // 创建订单元素
        function createOrderElement(orderData, rawText, index) {
            const currentLang = languageSelector.value;
            const orderDiv = document.createElement('div');
            orderDiv.className = 'order-item';
            orderDiv.style.padding = '1rem';
            orderDiv.style.border = '1px solid #e5e7eb';
            orderDiv.style.borderRadius = '0.5rem';
            
            // 创建订单标题
            const header = document.createElement('div');
            header.style.display = 'flex';
            header.style.justifyContent = 'space-between';
            header.style.alignItems = 'center';
            header.style.marginBottom = '1rem';
            header.style.paddingBottom = '0.5rem';
            header.style.borderBottom = '1px solid #e5e7eb';
            
            const title = document.createElement('h3');
            title.style.fontSize = '1rem';
            title.style.fontWeight = '500';
            title.style.margin = '0';
            title.textContent = `${i18n[currentLang]['order-number']} #${index} - ${orderData['ota-reference'] || '未知'}`;
            
            const copyButton = document.createElement('button');
            copyButton.className = 'btn btn-gray';
            copyButton.style.padding = '0.25rem 0.5rem';
            copyButton.style.fontSize = '0.875rem';
            copyButton.textContent = i18n[currentLang]['copy'];
            copyButton.addEventListener('click', () => copyOrderData(orderData));
            
            header.appendChild(title);
            header.appendChild(copyButton);
            orderDiv.appendChild(header);
            
            // 创建订单详情
            const details = document.createElement('div');
            details.style.display = 'grid';
            details.style.gridTemplateColumns = 'repeat(2, 1fr)';
            details.style.gap = '0.5rem';
            details.style.fontSize = '0.875rem';
            
            // 添加字段
            const fieldGroups = [
                { label: i18n[currentLang]['ota-platform'], value: orderData.ota },
                { label: i18n[currentLang]['order-number'], value: orderData['ota-reference'] },
                { label: i18n[currentLang]['price'], value: orderData.price },
                { label: i18n[currentLang]['passenger-name'], value: orderData.name },
                { label: i18n[currentLang]['phone'], value: orderData.phone },
                { label: i18n[currentLang]['email'], value: orderData.email },
                { label: i18n[currentLang]['flight-number'], value: orderData['flight-number'] },
                { label: i18n[currentLang]['pickup-time'], value: orderData['pickup-datetime'] },
                { label: i18n[currentLang]['pickup-address'], value: orderData['pickup-address'] },
                { label: i18n[currentLang]['dropoff-address'], value: orderData['dropoff-address'] },
                { label: i18n[currentLang]['car-type'], value: orderData['car-type'] },
                { label: i18n[currentLang]['luggage-count'], value: orderData['luggage-number'] },
                { label: i18n[currentLang]['passenger-count'], value: orderData['passenger-number'] },
                { label: i18n[currentLang]['order-type'], value: getOrderTypeLabel(orderData, currentLang) },
                { label: i18n[currentLang]['driving-region'], value: orderData['driving-region']?.toUpperCase() },
                { label: i18n[currentLang]['remarks'], value: orderData.remark }
            ];
            
            fieldGroups.forEach(field => {
                if (field.value) {
                    const fieldDiv = document.createElement('div');
                    fieldDiv.style.display = 'flex';
                    
                    const label = document.createElement('div');
                    label.style.fontWeight = '500';
                    label.style.width = '6rem';
                    label.textContent = field.label + '：';
                    
                    const value = document.createElement('div');
                    value.style.flex = '1';
                    value.textContent = field.value;
                    
                    fieldDiv.appendChild(label);
                    fieldDiv.appendChild(value);
                    details.appendChild(fieldDiv);
                }
            });
            
            orderDiv.appendChild(details);
            
            // 添加查看详细按钮
            const viewDetailsButton = document.createElement('button');
            viewDetailsButton.className = 'btn btn-primary';
            viewDetailsButton.style.marginTop = '1rem';
            viewDetailsButton.style.width = '100%';
            viewDetailsButton.textContent = i18n[currentLang]['view-details'];
            viewDetailsButton.addEventListener('click', () => viewDetailedOrder(orderData, rawText));
            
            orderDiv.appendChild(viewDetailsButton);
            
            return orderDiv;
        }

        // 获取订单类型标签（多语言支持）
        function getOrderTypeLabel(orderData, lang) {
            if (orderData.category === 'airport') {
                if (orderData.subcategory === 'pickup') {
                    return i18n[lang]['airport-pickup'];
                } else if (orderData.subcategory === 'dropoff') {
                    return i18n[lang]['airport-dropoff'];
                }
            }
            return i18n[lang]['charter'];
        }

        // 查看详细订单
        function viewDetailedOrder(orderData, rawText) {
            // 显示标准订单表单，隐藏多订单容器
            document.querySelector('.grid').classList.remove('hidden');
            multiOrdersContainer.classList.add('hidden');
            
            // 填充表单
            fillOrderForm(orderData);
            
            // 显示原始文本供参考
            rawOrderTextarea.value = rawText;
        }

        // 提取订单数据
        function extractOrderData(orderText) {
            const orderData = {
                ota: 'Kkday',
                language: 'English',  // Kkday的默认语言是英文
                driver: 1,
                category: '',
                subcategory: ''
            };
            
            // 根据规则映射提取字段
            for (const mapping of kkdayRule.fieldMappings) {
                try {
                    const regex = new RegExp(mapping.pattern, 'i');
                    const match = orderText.match(regex);
                    
                    if (match) {
                        const fieldId = mapping.field;
                        
                        // 特殊处理pickup-datetime字段 - 可能有多个捕获组
                        if (fieldId === 'pickup-datetime') {
                            // 查找第一个非undefined的捕获组
                            for (let i = 1; i < match.length; i++) {
                                if (match[i]) {
                                    // 如果是日期和时间分开的，需要合并
                                    if (i === 3 && match[4]) {
                                        orderData[fieldId] = `${match[3]} ${match[4]}`;
                                    } else {
                                        orderData[fieldId] = match[i].trim();
                                    }
                                    break;
                                }
                            }
                        } else if (match[1]) {
                            orderData[fieldId] = match[1].trim();
                        }
                    }
                } catch (e) {
                    console.error(`提取字段 ${mapping.field} 时出错:`, e);
                }
            }
            
            // 映射字段名称
            if (orderData.reference) {
                orderData['ota-reference'] = orderData.reference;
            }
            
            if (orderData.flight) {
                orderData['flight-number'] = orderData.flight;
            }
            
            if (orderData.passengers) {
                orderData['passenger-number'] = orderData.passengers;
            }
            
            if (orderData.luggage) {
                orderData['luggage-number'] = orderData.luggage;
            }
            
            // 识别订单类型
            identifyKkdayOrderType(orderText, orderData);
            
            // 判断驾驶区域
            determineDrivingRegion(orderText, orderData);
            
            return orderData;
        }

        // Kkday订单类型识别函数
        function identifyKkdayOrderType(orderText, orderData) {
            // 检查机场相关关键词
            const airportKeywords = ['机场', '航站', '航厦', '空港', 'airport', 'terminal', 'KLIA', 'KLIA2'];
            const hasAirportKeywords = airportKeywords.some(keyword => 
                orderText.toLowerCase().includes(keyword.toLowerCase())
            );

            if (hasAirportKeywords) {
                orderData.category = 'airport';
                
                // 根据地点判断是接机还是送机
                const pickupHasAirport = airportKeywords.some(keyword => 
                    (orderData['pickup-address'] || '').toLowerCase().includes(keyword.toLowerCase())
                );
                
                const dropoffHasAirport = airportKeywords.some(keyword => 
                    (orderData['dropoff-address'] || '').toLowerCase().includes(keyword.toLowerCase())
                );

                if (pickupHasAirport) {
                    orderData.subcategory = 'pickup';
                } else if (dropoffHasAirport) {
                    orderData.subcategory = 'dropoff';
                } else {
                    // 默认为接机
                    orderData.subcategory = 'pickup';
                }

                // 提取标题作为备注
                const titleMatch = orderText.match(/(?:商品名称|Product Name)：([^\n]+)/);
                if (titleMatch && titleMatch[1]) {
                    if (!orderData.remark) {
                        orderData.remark = titleMatch[1].trim();
                    } else {
                        orderData.remark = `${titleMatch[1].trim()} - ${orderData.remark}`;
                    }
                }
            } else {
                // 默认为包车服务
                orderData.category = 'charter';
                orderData.subcategory = '';
            }

            // 如果没有下车地点，则设置为与上车地点一致
            if (!orderData['dropoff-address'] && orderData['pickup-address']) {
                orderData['dropoff-address'] = orderData['pickup-address'];
            }
        }

        // 判断驾驶区域
        function determineDrivingRegion(orderText, orderData) {
            const regionMapping = [
                { code: 'kl', keywords: ['kuala lumpur', 'kl', 'selangor', '吉隆坡', '雪兰莪'] },
                { code: 'penang', keywords: ['penang', '槟城'] },
                { code: 'jb', keywords: ['johor', 'jb', '柔佛', '新山'] },
                { code: 'sabah', keywords: ['sabah', '沙巴', '亚庇', 'kota kinabalu', '仙本那'] },
                { code: 'sg', keywords: ['singapore', 'changi', '新加坡', '樟宜机场'] }
            ];
            
            const lowerOrderText = orderText.toLowerCase();
            
            for (const region of regionMapping) {
                if (region.keywords.some(keyword => lowerOrderText.includes(keyword.toLowerCase()))) {
                    orderData['driving-region'] = region.code;
                    return;
                }
            }
            
            // 默认设置为吉隆坡
            orderData['driving-region'] = 'kl';
        }

        // 处理中文地址中的POI翻译
        async function processAddress(address) {
            if (!address) return address;
            
            // 机场关键词映射
            const airportKeywords = {
                '樟宜机场': 'Changi Airport',
                '吉隆坡机场': 'Kuala Lumpur International Airport',
                '吉隆坡国际机场1': 'Kuala Lumpur International Airport Terminal 1',
                '吉隆坡国际机场2': 'Kuala Lumpur International Airport Terminal 2',
                '吉隆坡国际机场t1': 'Kuala Lumpur International Airport Terminal 1',
                '吉隆坡国际机场t2': 'Kuala Lumpur International Airport Terminal 2',
                '吉隆坡国际机场T1': 'Kuala Lumpur International Airport Terminal 1',
                '吉隆坡国际机场T2': 'Kuala Lumpur International Airport Terminal 2',
                '吉隆坡T1': 'Kuala Lumpur International Airport Terminal 1',
                '吉隆坡T2': 'Kuala Lumpur International Airport Terminal 2',
                'KLIA': 'Kuala Lumpur International Airport',
                'KLIA1': 'Kuala Lumpur International Airport Terminal 1',
                'KLIA2': 'Kuala Lumpur International Airport Terminal 2',
                'KLIAT1': 'Kuala Lumpur International Airport Terminal 1',
                'KLIAT2': 'Kuala Lumpur International Airport Terminal 2',
                'KLIA-T1': 'Kuala Lumpur International Airport Terminal 1',
                'KLIA-T2': 'Kuala Lumpur International Airport Terminal 2',
                '斗湖机场': 'Tawau Airport',
                '亚庇机场': 'Kota Kinabalu International Airport',
                '槟城机场': 'Penang International Airport',
                '新山机场': 'Senai International Airport',
                '沙巴机场': 'Kota Kinabalu International Airport',
                '仙本那机场': 'Semporna Airport',
                '新加坡机场': 'Changi International Airport',                    
            };
            
            // 通用机场关键词列表
            const genericAirportKeywords = ['机场', '航站楼', '航站', '航厦', '国际机场', 'airport', 'terminal', 'international'];
            
            // 计算字符串相似度 (Levenshtein距离)的函数
            function calculateStringSimilarity(str1, str2) {
                if (!str1 || !str2) return 0;
                
                // 创建矩阵
                const matrix = Array(str1.length + 1).fill().map(() => Array(str2.length + 1).fill(0));
                
                // 初始化矩阵
                for (let i = 0; i <= str1.length; i++) {
                    matrix[i][0] = i;
                }
                for (let j = 0; j <= str2.length; j++) {
                    matrix[0][j] = j;
                }
                
                // 填充矩阵
                for (let i = 1; i <= str1.length; i++) {
                    for (let j = 1; j <= str2.length; j++) {
                        const cost = str1[i - 1] === str2[j - 1] ? 0 : 1;
                        matrix[i][j] = Math.min(
                            matrix[i - 1][j] + 1,     // 删除
                            matrix[i][j - 1] + 1,     // 插入
                            matrix[i - 1][j - 1] + cost // 替换
                        );
                    }
                }
                
                // 计算相似度百分比
                const maxLength = Math.max(str1.length, str2.length);
                const distance = matrix[str1.length][str2.length];
                const similarity = ((maxLength - distance) / maxLength) * 100;
                
                return similarity;
            }
            
            // 将地址标准化（去除空格和标点）用于模糊匹配
            function normalizeString(str) {
                if (!str) return '';
                return str.toLowerCase().replace(/\s+/g, '').replace(/[.,\/#!$%\^&\*;:{}=\-_`~()]/g, '');
            }
            
            // 步骤1: 使用精确匹配+模糊匹配检查是否包含机场关键词
            let bestMatch = null;
            let bestMatchSimilarity = 0;
            let bestMatchKeyword = '';
            const normalizedAddress = normalizeString(address);
            
            // 首先尝试精确匹配
            for (const [keyword, translation] of Object.entries(airportKeywords)) {
                if (address.includes(keyword)) {
                    console.log(`精确匹配机场关键词: ${keyword} -> ${translation}`);
                    return address.replace(keyword, translation);
                }
                
                // 如果没有精确匹配，计算模糊匹配度
                const normalizedKeyword = normalizeString(keyword);
                
                // 对地址中的每个部分进行模糊匹配
                const addressParts = normalizedAddress.split(/[\s,\-\/\(\)\[\]]/g).filter(Boolean);
                for (const part of addressParts) {
                    const similarity = calculateStringSimilarity(part, normalizedKeyword);
                    if (similarity >= 80 && similarity > bestMatchSimilarity) {
                        bestMatchSimilarity = similarity;
                        bestMatch = translation;
                        bestMatchKeyword = keyword;
                    }
                }
            }
            
            // 如果找到模糊匹配且相似度超过80%
            if (bestMatch) {
                console.log(`模糊匹配机场关键词 (${bestMatchSimilarity.toFixed(2)}% 相似): ${bestMatchKeyword} -> ${bestMatch}`);
                
                // 尝试替换原始地址中与关键词相似的部分
                const regex = new RegExp(`\\b[\\w\\u4e00-\\u9fa5]+(?:机场|国际机场|国际|航站|T\\d|t\\d|1|2)\\b`, 'gi');
                let replaced = false;
                const result = address.replace(regex, (match) => {
                    const matchSimilarity = calculateStringSimilarity(normalizeString(match), normalizeString(bestMatchKeyword));
                    if (matchSimilarity >= 80) {
                        replaced = true;
                        return bestMatch;
                    }
                    return match;
                });
                
                if (replaced) {
                    return result;
                }
                
                // 如果无法通过正则表达式找到匹配部分，直接添加翻译
                return `${address} (${bestMatch})`;
            }
            
            // 步骤2: 检查是否包含通用机场关键词
            let containsAirportKeyword = false;
            for (const keyword of genericAirportKeywords) {
                if (address.toLowerCase().includes(keyword.toLowerCase())) {
                    console.log(`检测到通用机场关键词: ${keyword}`);
                    containsAirportKeyword = true;
                    break;
                }
            }
            
            if (containsAirportKeyword) {
                // 如果已经有英文机场关键词，直接返回
                if (/airport|terminal|international/i.test(address)) {
                    console.log(`地址已包含英文机场关键词，跳过翻译: ${address}`);
                    return address;
                }
                
                // 尝试提取城市名并构建机场名称
                const cityMatch = address.match(/^([^机场航站]+)/);
                if (cityMatch && cityMatch[1]) {
                    const cityName = cityMatch[1];
                    // 简单的城市名映射
                    const cityMapping = {
                        '吉隆坡': 'Kuala Lumpur',
                        '新加坡': 'Singapore',
                        '亚庇': 'Kota Kinabalu',
                        '沙巴': 'Sabah',
                        '槟城': 'Penang',
                        '新山': 'Johor Bahru',
                        '仙本那': 'Semporna'
                    };
                    
                    // 模糊匹配城市名
                    let bestCityMatch = null;
                    let bestCitySimilarity = 0;
                    
                    for (const [city, translation] of Object.entries(cityMapping)) {
                        const similarity = calculateStringSimilarity(normalizeString(cityName), normalizeString(city));
                        if (similarity >= 80 && similarity > bestCitySimilarity) {
                            bestCitySimilarity = similarity;
                            bestCityMatch = translation;
                        }
                    }
                    
                    if (bestCityMatch) {
                        console.log(`模糊匹配城市名 (${bestCitySimilarity.toFixed(2)}% 相似): ${cityName} -> ${bestCityMatch}`);
                        
                        const airportName = address.match(/([^\s]+(?:机场|航站楼|航站|航厦|国际机场))/);
                        if (airportName && airportName[1]) {
                            const englishAirportName = `${bestCityMatch} International Airport`;
                            console.log(`构建机场名称: ${airportName[1]} -> ${englishAirportName}`);
                            return address.replace(airportName[1], englishAirportName);
                        }
                    }
                }
                
                // 如果无法构建，返回原始地址
                console.log(`无法构建机场名称，保留原地址: ${address}`);
                return address;
            }
            
            // 如果不是机场相关，使用原有的POI翻译逻辑
            const poiRegex = /(.*?(?:小区|大厦|广场|中心|酒店|餐厅|商场|医院|学校))/;
            const match = address.match(poiRegex);
            
            if (match) {
                try {
                    const translatedPOI = await translateToEnglish(match[1]);
                    return address.replace(match[1], translatedPOI);
                } catch (error) {
                    console.error('翻译地址失败:', error);
                    return address;
                }
            }
            return address;
        }

        // Gemini API 翻译功能
        async function translateToEnglish(text) {
            if (!text || text.trim().length === 0) return text;
            
            try {
                // 这里使用模拟功能，实际项目中应替换为真实API调用
                console.log('尝试翻译:', text);
                // 简单模拟一下翻译效果
                return text + ' (Translated)';
            } catch (error) {
                console.error('翻译请求失败:', error);
                return text;
            }
        }

        // 填充订单表单
        function fillOrderForm(orderData) {
            // 填充表单字段
            for (const [field, value] of Object.entries(orderData)) {
                const input = document.getElementById(field);
                if (input) {
                    input.value = value;
                }
            }
        }

        // 启用表单字段编辑
        function enableFormEditing() {
            formInputs.forEach(input => {
                input.readOnly = false;
                input.disabled = false;
            });
            
            editBtn.classList.add('hidden');
            saveBtn.classList.remove('hidden');
        }

        // 禁用表单字段编辑
        function disableFormEditing() {
            formInputs.forEach(input => {
                input.readOnly = true;
                input.disabled = true;
            });
            
            saveBtn.classList.add('hidden');
            editBtn.classList.remove('hidden');
        }

        // 复制表单数据
        function copyFormData() {
            let formattedData = '';
            
            formInputs.forEach(input => {
                if (input.id && input.value) {
                    const label = input.previousElementSibling?.textContent || input.id;
                    formattedData += `${label}: ${input.value}\n`;
                }
            });
            
            copyToClipboard(formattedData);
            showNotification(i18n[languageSelector.value]['data-copied']);
        }

        // 复制订单数据
        function copyOrderData(orderData) {
            let formattedData = '';
            
            // 字段映射
            const fieldMapping = {
                'ota': 'OTA平台',
                'ota-reference': 'OTA订单号',
                'price': '价格',
                'name': '乘客姓名',
                'phone': '电话',
                'email': '邮箱',
                'flight-number': '航班号',
                'pickup-datetime': '接机时间',
                'pickup-address': '接机地址',
                'dropoff-address': '送机地址',
                'car-type': '车型',
                'luggage-number': '行李数量',
                'passenger-number': '乘客人数',
                'language': '语言',
                'category': '类别',
                'subcategory': '子类别',
                'driving-region': '驾驶区域',
                'driver': '司机数量',
                'remark': '备注'
            };
            
            // 格式化数据
            for (const [key, value] of Object.entries(orderData)) {
                if (value && fieldMapping[key]) {
                    let displayValue = value;
                    
                    if (key === 'category') {
                        displayValue = value === 'airport' ? '机场接送' : '包车';
                    } else if (key === 'subcategory' && orderData.category === 'airport') {
                        displayValue = value === 'pickup' ? '接机' : '送机';
                    } else if (key === 'driving-region') {
                        const regionNames = {
                            'kl': '吉隆坡',
                            'penang': '槟城',
                            'jb': '新山',
                            'sabah': '沙巴',
                            'sg': '新加坡'
                        };
                        displayValue = regionNames[value] || value;
                    }
                    
                    formattedData += `${fieldMapping[key]}: ${displayValue}\n`;
                }
            }
            
            copyToClipboard(formattedData);
            showNotification(i18n[languageSelector.value]['data-copied']);
        }

        // 复制内容到剪贴板
        function copyToClipboard(text) {
            const textarea = document.createElement('textarea');
            textarea.value = text;
            document.body.appendChild(textarea);
            textarea.select();
            document.execCommand('copy');
            document.body.removeChild(textarea);
        }

        // 显示通知
        function showNotification(message) {
            notification.textContent = message;
            notification.classList.add('show');
            
            setTimeout(() => {
                notification.classList.remove('show');
            }, 3000);
        }

        // 事件监听器
        convertBtn.addEventListener('click', convertOrder);
        
        resetBtn.addEventListener('click', function() {
            rawOrderTextarea.value = '';
        });
        
        editBtn.addEventListener('click', enableFormEditing);
        
        saveBtn.addEventListener('click', disableFormEditing);
        
        copyOutputBtn.addEventListener('click', copyFormData);

        // 语言选择器事件
        languageSelector.addEventListener('change', function() {
            const selectedLang = this.value;
            updateUILanguage(selectedLang);
        });

        // 页面加载时检查是否有来自sessionStorage的订单数据并处理
        document.addEventListener('DOMContentLoaded', function() {
            // 设置初始语言
            const savedLang = localStorage.getItem('preferred-language') || 'zh';
            languageSelector.value = savedLang;
            updateUILanguage(savedLang);
            
            const orderData = sessionStorage.getItem('orderData');
            if (orderData) {
                rawOrderTextarea.value = orderData;
                // 清除sessionStorage中的数据
                sessionStorage.removeItem('orderData');
                // 自动处理订单
                convertBtn.click();
            }
        });
    </script>
</body>
</html> 