/**
 * @file API配置文件 - 集中管理所有API密钥和配置
 * @description 支持开发和生产环境的API密钥配置文件
 * @warning 生产环境使用环境变量，开发环境可使用硬编码密钥
 * @todo 确保生产环境正确设置环境变量
 */

// ==================== 环境检测 ====================
/**
 * 检测当前运行环境
 * @returns {string} 环境类型
 */
function detectEnvironment() {
    // 检查是否在Netlify环境中
    if (typeof window !== 'undefined' && window.location) {
        const hostname = window.location.hostname;
        if (hostname.includes('netlify.app') || hostname.includes('netlify.com')) {
            return 'production';
        }
        if (hostname === 'localhost' || hostname === '127.0.0.1') {
            return 'development';
        }
    }
    return 'development';
}

// ==================== API密钥获取函数 ====================
/**
 * 安全获取API密钥 - 优先使用环境变量，回退到硬编码值
 * @param {string} envVarName - 环境变量名称
 * @param {string} fallbackKey - 回退密钥（开发环境使用）
 * @returns {string} API密钥
 */
function getApiKey(envVarName, fallbackKey) {
    const currentEnv = detectEnvironment();
    
    // 生产环境：尝试从环境变量获取（注意：前端无法直接访问环境变量）
    // 这里需要通过构建时注入或服务端代理的方式获取
    if (currentEnv === 'production') {
        // 在生产环境中，API密钥应该通过构建时注入或其他安全方式获取
        console.warn(`[API-CONFIG] 生产环境检测到，请确保 ${envVarName} 已正确配置`);
        // 生产环境暂时使用fallback，实际应该通过安全方式获取
        return fallbackKey;
    }
    
    // 开发环境：使用硬编码密钥
    console.info(`[API-CONFIG] 开发环境使用硬编码 ${envVarName}`);
    return fallbackKey;
}

// ==================== API密钥配置区域 ====================

/**
 * DeepSeek API配置
 * @description 用于智能订单解析和地址翻译
 * @apiUrl https://api.deepseek.com/v1/chat/completions
 */
const DEEPSEEK_CONFIG = {
    apiKey: getApiKey('DEEPSEEK_API_KEY', "***********************************"),
    baseUrl: "https://api.deepseek.com/v1",
    model: "deepseek-chat",
    timeout: 10000, // 10秒超时
    maxRetries: 2
};

/**
 * Gemini API配置
 * @description 用于酒店名称翻译和POI识别，使用最新的2.5 Flash Lite Preview模型
 * @apiUrl https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-lite-preview-06-17:generateContent
 */
const GEMINI_CONFIG = {
    apiKey: getApiKey('GEMINI_API_KEY', "AIzaSyDEycmjd2in4sexl61jnpysIJ4nzdeDa3s"),
    baseUrl: "https://generativelanguage.googleapis.com/v1beta/models",
    model: "gemini-2.5-flash-lite-preview-06-17", // 更新为最新的2.5 Flash Lite Preview模型
    temperature: 0.3, // 降低随机性，提高解析一致性
    timeout: 10000, // 10秒超时
    maxRetries: 2
};

/**
 * GoMyHire API配置
 * @description 用于订单创建和数据获取
 * @apiUrl https://staging.gomyhire.com.my/api
 */
const GOMYHIRE_CONFIG = {
    baseUrl: "https://staging.gomyhire.com.my/api",
    timeout: 15000, // 15秒超时
    endpoints: {
        backendUsers: "/backend_users",
        subCategories: "/sub_category",
        carTypes: "/car_types",
        createOrder: "/create_order"
    }
};

// ==================== API调用通用配置 ====================

/**
 * 通用请求配置
 */
const COMMON_CONFIG = {
    defaultTimeout: 10000,
    maxRetries: 2,
    retryDelay: 1000, // 重试延迟1秒
    headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'OrderRephase/1.0'
    }
};

/**
 * API调用统计配置
 */
const STATS_CONFIG = {
    enableLogging: true,
    logLevel: detectEnvironment() === 'production' ? 'WARN' : 'INFO',
    trackApiCalls: true,
    trackResponseTime: true
};

// ==================== 导出配置对象 ====================

/**
 * 统一的API配置对象
 * @description 包含所有API的配置信息和通用设置
 */
window.API_CONFIG = {
    // API服务配置
    deepseek: DEEPSEEK_CONFIG,
    gemini: GEMINI_CONFIG,
    gomyhire: GOMYHIRE_CONFIG,
    
    // 通用配置
    common: COMMON_CONFIG,
    stats: STATS_CONFIG,
    
    // 环境标识
    environment: detectEnvironment(),
    version: '1.1.0',
    
    // 安全提醒
    securityNote: detectEnvironment() === 'production' 
        ? '生产环境：请确保API密钥通过安全方式配置'
        : '开发环境：使用硬编码API密钥'
};

// ==================== 便捷访问函数 ====================

/**
 * @function getDeepSeekApiKey - 获取DeepSeek API密钥
 * @returns {string} DeepSeek API密钥
 */
window.getDeepSeekApiKey = () => DEEPSEEK_CONFIG.apiKey;

/**
 * @function getGeminiApiKey - 获取Gemini API密钥
 * @returns {string} Gemini API密钥
 */
window.getGeminiApiKey = () => GEMINI_CONFIG.apiKey;

/**
 * @function getApiConfig - 获取指定API的完整配置
 * @param {string} apiName - API名称 (deepseek, gemini, gomyhire)
 * @returns {Object} API配置对象
 */
window.getApiConfig = (apiName) => {
    const configs = {
        deepseek: DEEPSEEK_CONFIG,
        gemini: GEMINI_CONFIG,
        gomyhire: GOMYHIRE_CONFIG
    };
    return configs[apiName] || null;
};

/**
 * @function logApiCall - 记录API调用日志
 * @param {string} apiName - API名称
 * @param {string} operation - 操作类型
 * @param {Object} details - 详细信息
 */
window.logApiCall = (apiName, operation, details = {}) => {
    if (!STATS_CONFIG.enableLogging) return;
    
    const timestamp = new Date().toISOString();
    const logEntry = {
        timestamp,
        api: apiName,
        operation,
        environment: detectEnvironment(),
        ...details
    };
    
    console.log(`[API-CONFIG][${timestamp}][${apiName.toUpperCase()}] ${operation}`, logEntry);
};

// ==================== 环境配置验证 ====================
/**
 * 验证API配置完整性
 */
function validateApiConfig() {
    const env = detectEnvironment();
    const issues = [];
    
    if (!DEEPSEEK_CONFIG.apiKey || DEEPSEEK_CONFIG.apiKey === 'your-api-key') {
        issues.push('DeepSeek API密钥未正确配置');
    }
    
    if (!GEMINI_CONFIG.apiKey || GEMINI_CONFIG.apiKey === 'your-api-key') {
        issues.push('Gemini API密钥未正确配置');
    }
    
    if (issues.length > 0 && env === 'production') {
        console.error('[API-CONFIG] 生产环境配置问题:', issues);
    } else if (issues.length > 0) {
        console.warn('[API-CONFIG] 开发环境配置提醒:', issues);
    }
    
    return issues.length === 0;
}

// ==================== 初始化日志 ====================
const currentEnv = detectEnvironment();
console.log(`[API-CONFIG] 配置文件已加载 - ${currentEnv}环境模式`);

if (currentEnv === 'production') {
    console.info('[API-CONFIG] 生产环境：使用安全配置方式');
} else {
    console.warn('[API-CONFIG] 开发环境：使用硬编码API密钥，生产环境请使用环境变量');
}

// 验证配置
validateApiConfig();
