<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>通用订单处理器 - Universal Order Processor</title>
    <!-- API配置文件 - 统一管理API密钥 -->
    <script src="api-config.js"></script>
    <style>
        /* 基础样式 */
        :root {
            --primary-color: #3b82f6;
            --primary-hover: #2563eb;
            --secondary-color: #6b7280;
            --secondary-hover: #4b5563;
            --success-color: #10b981;
            --success-hover: #059669;
            --danger-color: #ef4444;
            --danger-hover: #dc2626;
            --warning-color: #f59e0b;
            --warning-hover: #d97706;
            --border-color: #e5e7eb;
            --bg-color: #f9fafb;
            --text-color: #111827;
            --text-secondary: #6b7280;
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-md: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --radius-sm: 0.25rem;
            --radius: 0.375rem;
            --radius-md: 0.5rem;
            --radius-lg: 0.75rem;
            --transition: all 0.2s ease-in-out;
        }
        
        * {
            box-sizing: border-box;
            -webkit-tap-highlight-color: transparent; /* 移除移动端点击高亮 */
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            background-color: var(--bg-color);
            color: var(--text-color);
            margin: 0;
            padding: 0;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            line-height: 1.5;
            font-size: 16px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 1rem;
            width: 100%;
            box-sizing: border-box;
        }
        
        header {
            background-color: white;
            border-bottom: 1px solid var(--border-color);
            padding: 1rem 0;
            box-shadow: var(--shadow-sm);
            position: -webkit-sticky;
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo-container {
            display: flex;
            align-items: center;
        }
        
        .logo-container img {
            height: 2rem;
            margin-right: 0.75rem;
            transition: var(--transition);
        }
        
        h1 {
            font-size: 1.25rem;
            font-weight: 600;
            margin: 0;
            color: var(--primary-color);
            transition: var(--transition);
        }
        
        main {
            flex: 1;
            padding: 1.5rem 0;
        }
        
        .card {
            background-color: white;
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow);
            border: 1px solid var(--border-color);
            overflow: hidden;
            margin-bottom: 1.5rem;
        }
        
        .card-header {
            background-color: #f8fafc;
            border-bottom: 1px solid var(--border-color);
            padding: 1rem 1.5rem;
        }
        
        .card-title {
            font-size: 1.125rem;
            font-weight: 600;
            margin: 0;
            color: var(--text-color);
        }
        
        .card-body {
            padding: 1.5rem;
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-label {
            display: block;
            font-weight: 500;
            margin-bottom: 0.5rem;
            color: var(--text-color);
            font-size: 0.875rem;
        }
        
        textarea {
            width: 100%;
            min-height: 200px;
            padding: 0.75rem;
            border: 1px solid var(--border-color);
            border-radius: var(--radius);
            font-size: 0.875rem;
            line-height: 1.5;
            resize: vertical;
            transition: var(--transition);
            font-family: inherit;
        }
        
        textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
        
        .button-group {
            display: flex;
            gap: 0.75rem;
            flex-wrap: wrap;
        }
        
        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 0.75rem 1.5rem;
            font-size: 0.875rem;
            font-weight: 500;
            border-radius: var(--radius);
            border: 1px solid transparent;
            cursor: pointer;
            transition: var(--transition);
            text-decoration: none;
            min-height: 2.5rem;
            white-space: nowrap;
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }
        
        .btn-primary:hover:not(:disabled) {
            background-color: var(--primary-hover);
            border-color: var(--primary-hover);
        }
        
        .btn-secondary {
            background-color: var(--secondary-color);
            color: white;
            border-color: var(--secondary-color);
        }
        
        .btn-secondary:hover:not(:disabled) {
            background-color: var(--secondary-hover);
            border-color: var(--secondary-hover);
        }
        
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }
        
        .flex-1 {
            flex: 1;
        }
        
        .hidden {
            display: none !important;
        }
        
        /* 通知样式 */
        .notification {
            position: fixed;
            top: 1rem;
            right: 1rem;
            background-color: var(--success-color);
            color: white;
            padding: 0.75rem 1rem;
            border-radius: var(--radius);
            box-shadow: var(--shadow-md);
            z-index: 1000;
            transform: translateX(100%);
            transition: transform 0.3s ease-in-out;
        }
        
        .notification.show {
            transform: translateX(0);
        }
        
        /* 加载动画 */
        .api-loader {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }
        
        .loader-content {
            background-color: white;
            padding: 2rem;
            border-radius: var(--radius-lg);
            text-align: center;
            box-shadow: var(--shadow-md);
        }
        
        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid var(--border-color);
            border-top: 4px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                padding: 0 0.75rem;
            }
            
            .card-body {
                padding: 1rem;
            }
            
            .button-group {
                flex-direction: column;
            }
            
            .btn {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <!-- 页面头部 -->
    <header>
        <div class="container">
            <div class="header-content">
                <div class="logo-container">
                    <img src="images/logo.svg" alt="Logo" onerror="this.style.display='none'">
                    <h1 data-i18n="app-title">通用订单处理器</h1>
                </div>
                <div class="header-controls">
                    <select id="language-selector" class="form-select">
                        <option value="zh">中文</option>
                        <option value="en">English</option>
                        <option value="ja">日本語</option>
                        <option value="ko">한국어</option>
                    </select>
                </div>
            </div>
        </div>
    </header>

    <!-- 主要内容区域 -->
    <main>
        <div class="container">
            <!-- 订单输入卡片 -->
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title" data-i18n="universal-processor-title">通用订单处理器</h2>
                </div>
                <div class="card-body">
                    <div class="form-group">
                        <label for="raw-order" class="form-label" data-i18n="paste-order-text">请粘贴订单文本</label>
                        <textarea 
                            id="raw-order" 
                            data-i18n-placeholder="paste-order-hint" 
                            placeholder="请粘贴任何格式的订单文本，系统将使用AI智能解析和正则表达式回退机制进行处理...">
                        </textarea>
                    </div>
                    <div class="button-group">
                        <button id="process-btn" class="btn btn-primary flex-1" data-i18n="process-order">处理订单</button>
                        <button id="reset-btn" class="btn btn-secondary" data-i18n="reset">重置</button>
                    </div>
                </div>
            </div>

            <!-- 处理结果区域 -->
            <div id="result-container" class="card hidden">
                <div class="card-header">
                    <h2 class="card-title" data-i18n="processing-result">处理结果</h2>
                </div>
                <div class="card-body">
                    <div id="result-content">
                        <!-- 处理结果将在这里显示 -->
                    </div>
                    <div class="button-group">
                        <button id="copy-result-btn" class="btn btn-primary" data-i18n="copy-result">复制结果</button>
                        <button id="back-to-home-btn" class="btn btn-secondary" data-i18n="back-to-home">返回首页</button>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- 通知组件 -->
    <div id="notification" class="notification"></div>

    <!-- 加载动画 -->
    <div id="api-loader" class="api-loader hidden">
        <div class="loader-content">
            <div class="spinner"></div>
            <p data-i18n="processing">正在处理订单，请稍候...</p>
        </div>
    </div>

    <script>
        // 多语言支持配置
        const i18n = {
            zh: {
                "app-title": "通用订单处理器",
                "universal-processor-title": "通用订单处理器",
                "paste-order-text": "请粘贴订单文本",
                "paste-order-hint": "请粘贴任何格式的订单文本，系统将使用AI智能解析和正则表达式回退机制进行处理...",
                "process-order": "处理订单",
                "reset": "重置",
                "processing-result": "处理结果",
                "copy-result": "复制结果",
                "back-to-home": "返回首页",
                "processing": "正在处理订单，请稍候...",
                "please-enter-data": "请先输入订单数据！",
                "processing-success": "订单处理完成！",
                "processing-failed": "订单处理失败，请检查数据格式或稍后重试。",
                "copied-to-clipboard": "结果已复制到剪贴板！",
                "copy-failed": "复制失败，请手动复制。"
            },
            en: {
                "app-title": "Universal Order Processor",
                "universal-processor-title": "Universal Order Processor",
                "paste-order-text": "Please paste order text",
                "paste-order-hint": "Please paste order text in any format, the system will use AI intelligent parsing and regex fallback mechanism for processing...",
                "process-order": "Process Order",
                "reset": "Reset",
                "processing-result": "Processing Result",
                "copy-result": "Copy Result",
                "back-to-home": "Back to Home",
                "processing": "Processing order, please wait...",
                "please-enter-data": "Please enter order data first!",
                "processing-success": "Order processing completed!",
                "processing-failed": "Order processing failed, please check data format or try again later.",
                "copied-to-clipboard": "Result copied to clipboard!",
                "copy-failed": "Copy failed, please copy manually."
            }
        };

        // 通用订单处理器核心类
        class UniversalOrderProcessor {
            constructor() {
                this.config = this.getDefaultConfig();
                this.translationCache = new Map(); // 地址翻译缓存
                this.processingStats = {
                    totalProcessed: 0,
                    aiParseSuccess: 0,
                    regexFallback: 0,
                    translationCalls: 0
                };

                this.initializeElements();
                this.initializeEventListeners();
                this.initializeFromSession();
                this.initializeI18n();
            }

            /**
             * 获取默认配置
             * @returns {Object} 默认配置对象
             */
            getDefaultConfig() {
                return {
                    // 通用订单数据结构
                    defaultOrderData: {
                        ota: '通用平台',
                        'ota-reference': '',
                        price: '',
                        name: '',
                        phone: '',
                        email: '',
                        'flight-number': '',
                        'pickup-datetime': '',
                        'pickup-address': '',
                        'dropoff-address': '',
                        'car-type': '',
                        'passenger-number': '1',
                        'luggage-number': '1',
                        language: 'Chinese',
                        category: 'airport',
                        subcategory: 'pickup',
                        'driving-region': 'kl',
                        driver: '1',
                        remark: ''
                    },
                    // AI解析配置
                    aiConfig: {
                        useDeepSeek: true,
                        temperature: 0.1,
                        maxTokens: 500
                    },
                    // 地址翻译配置
                    translationConfig: {
                        useGemini: true,
                        batchSize: 5,
                        cacheEnabled: true
                    }
                };
            }

            /**
             * 初始化DOM元素引用
             */
            initializeElements() {
                this.elements = {
                    rawOrderTextarea: document.getElementById('raw-order'),
                    processBtn: document.getElementById('process-btn'),
                    resetBtn: document.getElementById('reset-btn'),
                    resultContainer: document.getElementById('result-container'),
                    resultContent: document.getElementById('result-content'),
                    copyResultBtn: document.getElementById('copy-result-btn'),
                    backToHomeBtn: document.getElementById('back-to-home-btn'),
                    notification: document.getElementById('notification'),
                    apiLoader: document.getElementById('api-loader'),
                    languageSelector: document.getElementById('language-selector')
                };
            }

            /**
             * 初始化事件监听器
             */
            initializeEventListeners() {
                // 处理订单按钮
                this.elements.processBtn.addEventListener('click', () => this.processOrder());

                // 重置按钮
                this.elements.resetBtn.addEventListener('click', () => this.resetForm());

                // 复制结果按钮
                this.elements.copyResultBtn.addEventListener('click', () => this.copyResult());

                // 返回首页按钮
                this.elements.backToHomeBtn.addEventListener('click', () => this.backToHome());

                // 语言选择器
                this.elements.languageSelector.addEventListener('change', () => this.updateLanguage());

                // 键盘快捷键
                document.addEventListener('keydown', (e) => {
                    if (e.ctrlKey && e.key === 'Enter') {
                        this.processOrder();
                    }
                });
            }

            /**
             * 从会话存储初始化数据
             */
            initializeFromSession() {
                const orderData = sessionStorage.getItem('orderData');
                const orderProvider = sessionStorage.getItem('orderProvider');

                if (orderData) {
                    this.elements.rawOrderTextarea.value = orderData;
                    console.log('从会话存储加载订单数据:', orderProvider || '未知平台');

                    // 清除会话存储
                    sessionStorage.removeItem('orderData');
                    sessionStorage.removeItem('orderProvider');

                    // 自动处理订单
                    setTimeout(() => this.processOrder(), 500);
                }
            }

            /**
             * 初始化多语言支持
             */
            initializeI18n() {
                this.updateLanguage();
            }

            /**
             * 主要订单处理方法
             * @description 使用AI智能解析和正则表达式回退机制处理订单
             */
            async processOrder() {
                const rawOrderText = this.elements.rawOrderTextarea.value.trim();

                if (!rawOrderText) {
                    this.showNotification(this.getCurrentLangText('please-enter-data'), 'warning');
                    return;
                }

                // 显示加载动画
                this.showApiLoader();

                try {
                    console.log('开始处理通用订单:', rawOrderText);

                    // 检查是否为多订单
                    const orders = this.splitOrders(rawOrderText);
                    let processedResults = [];

                    if (orders.length > 1) {
                        // 批量处理多个订单
                        console.log(`检测到 ${orders.length} 个订单，开始批量处理`);
                        for (let i = 0; i < orders.length; i++) {
                            const orderData = await this.extractOrderData(orders[i]);
                            const processedData = await this.postProcessOrderData(orderData);
                            processedResults.push({
                                index: i + 1,
                                data: processedData,
                                originalText: orders[i]
                            });
                        }
                    } else {
                        // 处理单个订单
                        console.log('处理单个订单');
                        const orderData = await this.extractOrderData(rawOrderText);
                        const processedData = await this.postProcessOrderData(orderData);
                        processedResults.push({
                            index: 1,
                            data: processedData,
                            originalText: rawOrderText
                        });
                    }

                    // 显示处理结果
                    this.displayResults(processedResults);
                    this.showNotification(this.getCurrentLangText('processing-success'), 'success');

                    // 更新统计信息
                    this.processingStats.totalProcessed += processedResults.length;
                    console.log('处理统计:', this.processingStats);

                } catch (error) {
                    console.error('订单处理失败:', error);
                    this.showNotification(this.getCurrentLangText('processing-failed'), 'error');
                } finally {
                    this.hideApiLoader();
                }
            }

            /**
             * 分割多个订单
             * @param {string} rawOrderText - 原始订单文本
             * @returns {Array} 订单数组
             */
            splitOrders(rawOrderText) {
                // 通用的订单分割逻辑，支持多种格式
                const splitPatterns = [
                    /订单编号[：:]\s*\d+/g,           // 中文订单编号
                    /Order\s*(?:Number|ID)[：:]?\s*\w+/gi,  // 英文订单编号
                    /订单[：:]/g,                      // 简单订单标识
                    /Order[：:]/gi                     // 英文订单标识
                ];

                let orders = [rawOrderText]; // 默认单订单

                for (const pattern of splitPatterns) {
                    const matches = [...rawOrderText.matchAll(pattern)];
                    if (matches.length > 1) {
                        // 找到多个匹配，进行分割
                        orders = [];
                        let lastIndex = 0;

                        matches.forEach((match, index) => {
                            if (index > 0) {
                                const orderText = rawOrderText.substring(lastIndex, match.index).trim();
                                if (orderText) orders.push(orderText);
                            }
                            lastIndex = match.index;
                        });

                        // 添加最后一个订单
                        const lastOrder = rawOrderText.substring(lastIndex).trim();
                        if (lastOrder) orders.push(lastOrder);

                        break; // 找到有效分割就停止
                    }
                }

                return orders.filter(order => order.length > 10); // 过滤太短的文本
            }

            /**
             * 提取订单数据 - 核心解析方法
             * @param {string} orderText - 订单文本
             * @returns {Promise<Object>} 解析后的订单数据
             */
            async extractOrderData(orderText) {
                console.log('开始提取订单数据:', orderText.substring(0, 100) + '...');

                try {
                    // 首先尝试AI智能解析
                    if (this.config.aiConfig.useDeepSeek) {
                        console.log('尝试使用DeepSeek AI进行智能解析...');
                        const aiResult = await this.parseWithAI(orderText);

                        if (aiResult && Object.keys(aiResult).length > 3) {
                            console.log('AI解析成功');
                            this.processingStats.aiParseSuccess++;
                            return this.validateAndCleanOrderData(aiResult, orderText);
                        }
                    }
                } catch (error) {
                    console.warn('AI解析失败，回退到正则表达式:', error);
                }

                // 回退到正则表达式解析
                console.log('使用正则表达式进行解析...');
                this.processingStats.regexFallback++;
                const regexResult = this.parseWithRegex(orderText);
                return this.validateAndCleanOrderData(regexResult, orderText);
            }

            /**
             * 使用AI进行订单解析
             * @param {string} orderText - 订单文本
             * @returns {Promise<Object>} AI解析结果
             */
            async parseWithAI(orderText) {
                if (!window.getDeepSeekApiKey()) {
                    throw new Error('DeepSeek API密钥未配置');
                }

                const systemPrompt = `你是一个专业的订单数据提取助手。请从订单文本中提取结构化数据，并返回严格的JSON格式。

提取规则：
1. 识别订单基本信息：订单号、价格、客户姓名
2. 识别时间信息：日期、时间（统一格式化为 YYYY-MM-DD HH:MM）
3. 识别地址信息：上车地址、下车地址
4. 识别服务信息：车型、乘客数量、行李数量
5. 识别联系信息：电话、邮箱
6. 识别航班信息：航班号（如果有）
7. 自动判断服务类型：接机(pickup)、送机(dropoff)、包车(charter)

必须返回以下JSON格式，缺失字段用空字符串：
{
  "ota": "通用平台",
  "ota-reference": "",
  "price": "",
  "name": "",
  "phone": "",
  "email": "",
  "flight-number": "",
  "pickup-datetime": "",
  "pickup-address": "",
  "dropoff-address": "",
  "car-type": "",
  "passenger-number": "",
  "luggage-number": "",
  "category": "",
  "subcategory": "",
  "remark": ""
}`;

                const response = await fetch("https://api.deepseek.com/chat/completions", {
                    method: "POST",
                    headers: {
                        "Authorization": `Bearer ${window.getDeepSeekApiKey()}`,
                        "Content-Type": "application/json"
                    },
                    body: JSON.stringify({
                        model: "deepseek-chat",
                        messages: [
                            { role: "system", content: systemPrompt },
                            { role: "user", content: `请解析以下订单文本：\n\n${orderText}` }
                        ],
                        temperature: this.config.aiConfig.temperature,
                        max_tokens: this.config.aiConfig.maxTokens,
                        stream: false
                    })
                });

                const data = await response.json();

                if (data?.choices?.[0]?.message?.content) {
                    let result = data.choices[0].message.content.trim();

                    // 提取JSON内容
                    const jsonMatch = result.match(/\{[\s\S]*\}/);
                    if (jsonMatch) {
                        result = jsonMatch[0];
                    }

                    return JSON.parse(result);
                }

                throw new Error('AI解析返回无效结果');
            }

            /**
             * 使用正则表达式进行订单解析
             * @param {string} orderText - 订单文本
             * @returns {Object} 正则解析结果
             */
            parseWithRegex(orderText) {
                const orderData = { ...this.config.defaultOrderData };

                // 通用字段提取规则
                const fieldPatterns = [
                    // 订单号提取
                    { field: 'ota-reference', patterns: [
                        /订单编?号[：:]\s*([A-Za-z0-9]+)/i,
                        /Order\s*(?:Number|ID)[：:]?\s*([A-Za-z0-9]+)/i,
                        /Reference[：:]?\s*([A-Za-z0-9]+)/i
                    ]},

                    // 价格提取
                    { field: 'price', patterns: [
                        /价格[：:]\s*([¥$€£]?\d+(?:\.\d{2})?)/i,
                        /Price[：:]?\s*([¥$€£]?\d+(?:\.\d{2})?)/i,
                        /金额[：:]\s*([¥$€£]?\d+(?:\.\d{2})?)/i,
                        /Amount[：:]?\s*([¥$€£]?\d+(?:\.\d{2})?)/i
                    ]},

                    // 姓名提取
                    { field: 'name', patterns: [
                        /姓名[：:]\s*([^\n\r,，]+)/i,
                        /Name[：:]?\s*([^\n\r,，]+)/i,
                        /客户[：:]\s*([^\n\r,，]+)/i,
                        /Customer[：:]?\s*([^\n\r,，]+)/i
                    ]},

                    // 电话提取
                    { field: 'phone', patterns: [
                        /电话[：:]\s*([+\d\s\-()]+)/i,
                        /Phone[：:]?\s*([+\d\s\-()]+)/i,
                        /手机[：:]\s*([+\d\s\-()]+)/i,
                        /Mobile[：:]?\s*([+\d\s\-()]+)/i,
                        /(\+?\d{1,4}[\s\-]?\d{3,4}[\s\-]?\d{3,4}[\s\-]?\d{3,4})/
                    ]},

                    // 邮箱提取
                    { field: 'email', patterns: [
                        /邮箱[：:]\s*([^\s@]+@[^\s@]+\.[^\s@]+)/i,
                        /Email[：:]?\s*([^\s@]+@[^\s@]+\.[^\s@]+)/i,
                        /([^\s@]+@[^\s@]+\.[^\s@]+)/
                    ]},

                    // 航班号提取
                    { field: 'flight-number', patterns: [
                        /航班[号]?[：:]\s*([A-Z]{2,3}\d{3,4})/i,
                        /Flight[：:]?\s*([A-Z]{2,3}\d{3,4})/i,
                        /([A-Z]{2,3}\d{3,4})/
                    ]},

                    // 时间提取
                    { field: 'pickup-datetime', patterns: [
                        /时间[：:]\s*(\d{4}[-/]\d{1,2}[-/]\d{1,2}\s+\d{1,2}:\d{2})/i,
                        /Date[：:]?\s*(\d{4}[-/]\d{1,2}[-/]\d{1,2}\s+\d{1,2}:\d{2})/i,
                        /(\d{4}[-/]\d{1,2}[-/]\d{1,2}\s+\d{1,2}:\d{2})/,
                        /(\d{1,2}[-/]\d{1,2}[-/]\d{4}\s+\d{1,2}:\d{2})/
                    ]},

                    // 地址提取
                    { field: 'pickup-address', patterns: [
                        /(?:上车|接机|出发)(?:地点|地址)?[：:]\s*([^\n\r]+)/i,
                        /(?:Pickup|From)[：:]?\s*([^\n\r]+)/i
                    ]},

                    { field: 'dropoff-address', patterns: [
                        /(?:下车|送机|目的)(?:地点|地址)?[：:]\s*([^\n\r]+)/i,
                        /(?:Dropoff|To|Destination)[：:]?\s*([^\n\r]+)/i
                    ]},

                    // 车型提取
                    { field: 'car-type', patterns: [
                        /车型[：:]\s*([^\n\r]+)/i,
                        /Vehicle[：:]?\s*([^\n\r]+)/i,
                        /(\d+座[车]?)/i,
                        /(sedan|suv|mpv|van)/i
                    ]},

                    // 乘客数量
                    { field: 'passenger-number', patterns: [
                        /乘客[：:]\s*(\d+)/i,
                        /Passenger[s]?[：:]?\s*(\d+)/i,
                        /(\d+)\s*人/i
                    ]},

                    // 行李数量
                    { field: 'luggage-number', patterns: [
                        /行李[：:]\s*(\d+)/i,
                        /Luggage[：:]?\s*(\d+)/i,
                        /(\d+)\s*件/i
                    ]}
                ];

                // 应用字段提取规则
                fieldPatterns.forEach(fieldConfig => {
                    for (const pattern of fieldConfig.patterns) {
                        const match = orderText.match(pattern);
                        if (match && match[1]) {
                            orderData[fieldConfig.field] = match[1].trim();
                            break; // 找到匹配就停止
                        }
                    }
                });

                // 智能判断服务类型
                this.determineServiceType(orderText, orderData);

                // 智能判断驾驶区域
                this.determineDrivingRegion(orderText, orderData);

                return orderData;
            }

            /**
             * 判断服务类型（接机、送机、包车）
             * @param {string} orderText - 订单文本
             * @param {Object} orderData - 订单数据对象
             */
            determineServiceType(orderText, orderData) {
                const lowerText = orderText.toLowerCase();

                // 关键词检测
                const pickupKeywords = ['接机', '接送机', '接车', '机场接', 'airport pickup', 'pick up', 'arrival'];
                const dropoffKeywords = ['送机', '去机场', '机场送', 'airport drop', 'departure', 'send to airport'];
                const charterKeywords = ['包车', '一日游', '半日游', '城市游', 'charter', 'day tour', 'city tour'];

                const hasPickup = pickupKeywords.some(keyword => lowerText.includes(keyword));
                const hasDropoff = dropoffKeywords.some(keyword => lowerText.includes(keyword));
                const hasCharter = charterKeywords.some(keyword => lowerText.includes(keyword));

                if (hasCharter) {
                    orderData.category = 'charter';
                    orderData.subcategory = 'charter';
                } else if (hasPickup && !hasDropoff) {
                    orderData.category = 'airport';
                    orderData.subcategory = 'pickup';
                } else if (hasDropoff && !hasPickup) {
                    orderData.category = 'airport';
                    orderData.subcategory = 'dropoff';
                } else {
                    // 根据地址判断
                    const airportKeywords = ['airport', '机场', 'klia', 'changi', 'terminal'];
                    const pickupHasAirport = airportKeywords.some(keyword =>
                        (orderData['pickup-address'] || '').toLowerCase().includes(keyword)
                    );
                    const dropoffHasAirport = airportKeywords.some(keyword =>
                        (orderData['dropoff-address'] || '').toLowerCase().includes(keyword)
                    );

                    if (pickupHasAirport) {
                        orderData.category = 'airport';
                        orderData.subcategory = 'pickup';
                    } else if (dropoffHasAirport) {
                        orderData.category = 'airport';
                        orderData.subcategory = 'dropoff';
                    } else {
                        // 默认为接机
                        orderData.category = 'airport';
                        orderData.subcategory = 'pickup';
                    }
                }
            }

            /**
             * 判断驾驶区域
             * @param {string} orderText - 订单文本
             * @param {Object} orderData - 订单数据对象
             */
            determineDrivingRegion(orderText, orderData) {
                const lowerText = orderText.toLowerCase();

                // 区域关键词映射
                const regionKeywords = {
                    'sg': ['singapore', 'changi', '新加坡', 'sg'],
                    'kk': ['kota kinabalu', 'sabah', '沙巴', '亚庇', 'kk'],
                    'penang': ['penang', '槟城', 'george town'],
                    'johor': ['johor', '柔佛', 'jb'],
                    'kl': ['kuala lumpur', 'klia', 'kl', '吉隆坡', '雪兰莪']
                };

                for (const [region, keywords] of Object.entries(regionKeywords)) {
                    if (keywords.some(keyword => lowerText.includes(keyword))) {
                        orderData['driving-region'] = region;
                        return;
                    }
                }

                // 默认为吉隆坡
                orderData['driving-region'] = 'kl';
            }

            /**
             * 验证和清洗订单数据
             * @param {Object} orderData - 原始订单数据
             * @param {string} originalText - 原始订单文本
             * @returns {Object} 清洗后的订单数据
             */
            validateAndCleanOrderData(orderData, originalText) {
                const cleanedData = { ...this.config.defaultOrderData };

                // 复制有效数据
                Object.keys(orderData).forEach(key => {
                    if (orderData[key] && orderData[key].toString().trim() !== '') {
                        cleanedData[key] = orderData[key].toString().trim();
                    }
                });

                // 数据格式化和验证
                this.formatOrderData(cleanedData);

                // 智能车型选择（基于乘客数量）
                this.smartVehicleSelection(cleanedData);

                // 电话号码增强（使用订单号作为回退）
                this.enhancePhoneNumber(cleanedData, originalText);

                return cleanedData;
            }

            /**
             * 格式化订单数据
             * @param {Object} orderData - 订单数据
             */
            formatOrderData(orderData) {
                // 格式化电话号码
                if (orderData.phone) {
                    orderData.phone = orderData.phone.replace(/[^\d+\-\s()]/g, '').trim();
                }

                // 格式化价格
                if (orderData.price) {
                    orderData.price = orderData.price.replace(/[^\d.]/g, '');
                }

                // 格式化乘客和行李数量
                ['passenger-number', 'luggage-number'].forEach(field => {
                    if (orderData[field]) {
                        const num = parseInt(orderData[field]);
                        orderData[field] = isNaN(num) ? '1' : num.toString();
                    }
                });

                // 格式化日期时间
                if (orderData['pickup-datetime']) {
                    orderData['pickup-datetime'] = this.formatDateTime(orderData['pickup-datetime']);
                }
            }

            /**
             * 智能车型选择
             * @param {Object} orderData - 订单数据
             */
            smartVehicleSelection(orderData) {
                if (!orderData['car-type'] || orderData['car-type'] === '') {
                    const passengerCount = parseInt(orderData['passenger-number']) || 1;

                    if (passengerCount <= 4) {
                        orderData['car-type'] = 'sedan';
                    } else if (passengerCount <= 7) {
                        orderData['car-type'] = 'SUV';
                    } else {
                        orderData['car-type'] = 'van';
                    }

                    console.log(`智能车型选择: ${passengerCount}人 -> ${orderData['car-type']}`);
                }
            }

            /**
             * 增强电话号码（使用订单号作为回退）
             * @param {Object} orderData - 订单数据
             * @param {string} originalText - 原始文本
             */
            enhancePhoneNumber(orderData, originalText) {
                if (!orderData.phone && orderData['ota-reference']) {
                    orderData.phone = orderData['ota-reference'];
                    console.log('使用订单号作为电话号码回退');
                }
            }

            /**
             * 格式化日期时间
             * @param {string} dateTimeStr - 日期时间字符串
             * @returns {string} 格式化后的日期时间
             */
            formatDateTime(dateTimeStr) {
                try {
                    const date = new Date(dateTimeStr);
                    if (!isNaN(date.getTime())) {
                        return date.toISOString().slice(0, 16).replace('T', ' ');
                    }
                } catch (error) {
                    console.warn('日期格式化失败:', dateTimeStr);
                }
                return dateTimeStr;
            }

            /**
             * 后处理订单数据（包括地址翻译）
             * @param {Object} orderData - 订单数据
             * @returns {Promise<Object>} 后处理后的订单数据
             */
            async postProcessOrderData(orderData) {
                // 地址翻译
                if (this.config.translationConfig.useGemini) {
                    if (orderData['pickup-address']) {
                        orderData['pickup-address'] = await this.translateAddress(orderData['pickup-address']);
                    }
                    if (orderData['dropoff-address']) {
                        orderData['dropoff-address'] = await this.translateAddress(orderData['dropoff-address']);
                    }
                }

                // 包车服务地址默认逻辑
                if (orderData.category === 'charter') {
                    if (!orderData['pickup-address'] && orderData['dropoff-address']) {
                        orderData['pickup-address'] = orderData['dropoff-address'];
                    } else if (!orderData['dropoff-address'] && orderData['pickup-address']) {
                        orderData['dropoff-address'] = orderData['pickup-address'];
                    }
                }

                return orderData;
            }

            /**
             * 地址翻译方法
             * @param {string} address - 原始地址
             * @returns {Promise<string>} 翻译后的地址
             */
            async translateAddress(address) {
                if (!address || address.trim() === '') return address;

                // 检查缓存
                if (this.config.translationConfig.cacheEnabled && this.translationCache.has(address)) {
                    console.log('使用缓存的地址翻译:', address);
                    return this.translationCache.get(address);
                }

                // 检查是否已经是英文
                if (this.isEnglishAddress(address)) {
                    console.log('地址已是英文，跳过翻译:', address);
                    return address;
                }

                try {
                    console.log('开始翻译地址:', address);
                    this.processingStats.translationCalls++;

                    const translatedAddress = await this.callGeminiTranslation(address);

                    // 缓存结果
                    if (this.config.translationConfig.cacheEnabled) {
                        this.translationCache.set(address, translatedAddress);
                    }

                    console.log('地址翻译完成:', address, '->', translatedAddress);
                    return translatedAddress;

                } catch (error) {
                    console.warn('地址翻译失败，返回原地址:', error);
                    return address;
                }
            }

            /**
             * 检查是否为英文地址
             * @param {string} address - 地址字符串
             * @returns {boolean} 是否为英文地址
             */
            isEnglishAddress(address) {
                const chineseRegex = /[\u4e00-\u9fff]/;
                return !chineseRegex.test(address);
            }

            /**
             * 调用Gemini API进行地址翻译
             * @param {string} address - 原始地址
             * @returns {Promise<string>} 翻译后的地址
             */
            async callGeminiTranslation(address) {
                if (!window.getGeminiApiKey()) {
                    throw new Error('Gemini API密钥未配置');
                }

                const geminiConfig = window.getApiConfig('gemini');
                const prompt = `请将以下中文地址翻译为英文，保持地址的准确性和可识别性。只返回翻译后的地址，不要添加任何解释：

${address}`;

                const response = await fetch(
                    `${geminiConfig.baseUrl}/${geminiConfig.model}:generateContent?key=${geminiConfig.apiKey}`,
                    {
                        method: "POST",
                        headers: {
                            "Content-Type": "application/json"
                        },
                        body: JSON.stringify({
                            contents: [{
                                parts: [{ text: prompt }]
                            }],
                            generationConfig: {
                                temperature: geminiConfig.temperature || 0.3,
                                maxOutputTokens: 200
                            }
                        })
                    }
                );

                const data = await response.json();

                if (data?.candidates?.[0]?.content?.parts?.[0]?.text) {
                    return data.candidates[0].content.parts[0].text.trim();
                }

                throw new Error('Gemini翻译返回无效结果');
            }

            /**
             * 显示处理结果
             * @param {Array} results - 处理结果数组
             */
            displayResults(results) {
                const resultContent = this.elements.resultContent;
                resultContent.innerHTML = '';

                if (results.length === 1) {
                    // 单个订单结果
                    const result = results[0];
                    resultContent.innerHTML = this.formatSingleOrderResult(result.data);
                } else {
                    // 多个订单结果
                    resultContent.innerHTML = this.formatMultipleOrderResults(results);
                }

                // 显示结果容器
                this.elements.resultContainer.classList.remove('hidden');

                // 滚动到结果区域
                this.elements.resultContainer.scrollIntoView({ behavior: 'smooth' });
            }

            /**
             * 格式化单个订单结果
             * @param {Object} orderData - 订单数据
             * @returns {string} 格式化的HTML
             */
            formatSingleOrderResult(orderData) {
                const fieldLabels = {
                    'ota': 'OTA平台',
                    'ota-reference': 'OTA订单号',
                    'price': '价格',
                    'name': '乘客姓名',
                    'phone': '电话',
                    'email': '邮箱',
                    'flight-number': '航班号',
                    'pickup-datetime': '接机时间',
                    'pickup-address': '接机地址',
                    'dropoff-address': '送机地址',
                    'car-type': '车型',
                    'passenger-number': '乘客人数',
                    'luggage-number': '行李数量',
                    'language': '语言',
                    'category': '类别',
                    'subcategory': '子类别',
                    'driving-region': '驾驶区域',
                    'driver': '司机数量',
                    'remark': '备注'
                };

                let html = '<div class="order-result">';

                Object.entries(orderData).forEach(([key, value]) => {
                    if (value && value.toString().trim() !== '' && fieldLabels[key]) {
                        html += `
                            <div class="result-field">
                                <span class="field-label">${fieldLabels[key]}:</span>
                                <span class="field-value">${this.escapeHtml(value)}</span>
                            </div>
                        `;
                    }
                });

                html += '</div>';
                return html;
            }

            /**
             * 格式化多个订单结果
             * @param {Array} results - 结果数组
             * @returns {string} 格式化的HTML
             */
            formatMultipleOrderResults(results) {
                let html = `<div class="multiple-orders-result">
                    <h3>批量处理结果 (${results.length} 个订单)</h3>
                `;

                results.forEach((result, index) => {
                    html += `
                        <div class="order-item">
                            <h4>订单 ${result.index}</h4>
                            ${this.formatSingleOrderResult(result.data)}
                        </div>
                    `;
                });

                html += '</div>';
                return html;
            }

            /**
             * HTML转义
             * @param {string} text - 原始文本
             * @returns {string} 转义后的文本
             */
            escapeHtml(text) {
                const div = document.createElement('div');
                div.textContent = text;
                return div.innerHTML;
            }

            /**
             * 复制结果到剪贴板
             */
            async copyResult() {
                try {
                    const resultText = this.elements.resultContent.innerText;
                    await navigator.clipboard.writeText(resultText);
                    this.showNotification(this.getCurrentLangText('copied-to-clipboard'), 'success');
                } catch (error) {
                    console.error('复制失败:', error);
                    this.showNotification(this.getCurrentLangText('copy-failed'), 'error');
                }
            }

            /**
             * 重置表单
             */
            resetForm() {
                this.elements.rawOrderTextarea.value = '';
                this.elements.resultContainer.classList.add('hidden');
                this.elements.rawOrderTextarea.focus();
            }

            /**
             * 返回首页
             */
            backToHome() {
                window.location.href = 'index.html';
            }

            /**
             * 显示通知
             * @param {string} message - 通知消息
             * @param {string} type - 通知类型 (success, warning, error)
             */
            showNotification(message, type = 'success') {
                const notification = this.elements.notification;
                notification.textContent = message;
                notification.className = `notification ${type}`;
                notification.classList.add('show');

                setTimeout(() => {
                    notification.classList.remove('show');
                }, 3000);
            }

            /**
             * 显示API加载动画
             */
            showApiLoader() {
                this.elements.apiLoader.classList.remove('hidden');
            }

            /**
             * 隐藏API加载动画
             */
            hideApiLoader() {
                this.elements.apiLoader.classList.add('hidden');
            }

            /**
             * 更新语言
             */
            updateLanguage() {
                const currentLang = this.elements.languageSelector.value;

                // 更新所有带有 data-i18n 属性的元素
                document.querySelectorAll('[data-i18n]').forEach(element => {
                    const key = element.getAttribute('data-i18n');
                    if (i18n[currentLang] && i18n[currentLang][key]) {
                        element.textContent = i18n[currentLang][key];
                    }
                });

                // 更新占位符文本
                document.querySelectorAll('[data-i18n-placeholder]').forEach(element => {
                    const key = element.getAttribute('data-i18n-placeholder');
                    if (i18n[currentLang] && i18n[currentLang][key]) {
                        element.placeholder = i18n[currentLang][key];
                    }
                });
            }

            /**
             * 获取当前语言的文本
             * @param {string} key - 文本键
             * @returns {string} 本地化文本
             */
            getCurrentLangText(key) {
                const currentLang = this.elements.languageSelector.value;
                return (i18n[currentLang] && i18n[currentLang][key]) || key;
            }
        }

        // 添加结果显示样式
        const additionalStyles = `
            <style>
                .order-result {
                    background-color: #f8fafc;
                    border: 1px solid var(--border-color);
                    border-radius: var(--radius);
                    padding: 1rem;
                    margin-bottom: 1rem;
                }

                .result-field {
                    display: flex;
                    margin-bottom: 0.5rem;
                    padding: 0.25rem 0;
                    border-bottom: 1px solid #e2e8f0;
                }

                .result-field:last-child {
                    border-bottom: none;
                    margin-bottom: 0;
                }

                .field-label {
                    font-weight: 600;
                    color: var(--text-color);
                    min-width: 120px;
                    flex-shrink: 0;
                }

                .field-value {
                    color: var(--text-secondary);
                    flex: 1;
                    word-break: break-word;
                }

                .multiple-orders-result h3 {
                    color: var(--primary-color);
                    margin-bottom: 1.5rem;
                    text-align: center;
                }

                .order-item {
                    margin-bottom: 2rem;
                    padding-bottom: 1.5rem;
                    border-bottom: 2px solid var(--border-color);
                }

                .order-item:last-child {
                    border-bottom: none;
                    margin-bottom: 0;
                }

                .order-item h4 {
                    color: var(--primary-color);
                    margin-bottom: 1rem;
                    font-size: 1.1rem;
                }

                .notification.warning {
                    background-color: var(--warning-color);
                }

                .notification.error {
                    background-color: var(--danger-color);
                }

                .form-select {
                    padding: 0.5rem;
                    border: 1px solid var(--border-color);
                    border-radius: var(--radius);
                    background-color: white;
                    font-size: 0.875rem;
                    cursor: pointer;
                }

                .form-select:focus {
                    outline: none;
                    border-color: var(--primary-color);
                    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
                }
            </style>
        `;

        // 注入额外样式
        document.head.insertAdjacentHTML('beforeend', additionalStyles);

        // 初始化应用
        document.addEventListener('DOMContentLoaded', () => {
            console.log('通用订单处理器初始化...');
            window.universalProcessor = new UniversalOrderProcessor();
            console.log('通用订单处理器初始化完成');
        });
    </script>
</body>
</html>
